# 搜索結果重疊區域顯示增強任務

## 任務目標
在 TongRAG3 搜索結果中清楚區分和顯示每個文檔塊的：
- 🟨 **重疊區域**：與前一個塊重疊的內容（完整顯示）
- 🟩 **一般區域**：該塊獨有的新內容（截斷顯示+剩餘字數）

## 用戶需求分析
用戶希望在搜索結果列表中就能：
1. 完整看到重疊部分內容（通常 100 字符左右）
2. 預覽一般部分內容（截斷顯示，避免過長）
3. 快速判斷是否已閱讀過重疊內容
4. 點擊查看完整詳細內容時，看到三部分：重疊區、一般區、全部組合

## 期望顯示效果

### 搜索結果列表顯示：
```
[2] 相關度: 71.1% | 來源: 部門rule宣導_20250509.md
    🔗 [重疊區域]
       來源塊: 塊 2 (400-926)
    內容預覽:
    🟨 重疊區: 4塊3.Friday, October 18, 2024 9:20 AM 由Xu Yongyu 徐永雨mail通知,mail主旨:G948程式配件寄送
              6.MSEC_CP_小die的限制 , 1.
    🟩 一般區: 一般 Prober 限制 :Die size (含 scribe line) 必須...（還有 385 個字）
```

### 詳細內容查看：
```
=== 文檔塊 #2 詳細內容 ===
字符範圍: 400-926
文檔塊ID: 部門rule宣導_20250509.md_chunk_2
是否有重疊: 是

📊 重疊分析結果
理論重疊: 100 字符
實際檢測: 100 字符

🟨 重疊部分 (100 字符, 19.0%):
----------------------------------------
:4塊3.Friday, October 18, 2024 9:20 AM 由Xu Yongyu 徐永雨mail通知,mail主旨:G948程式配件寄送
6.MSEC_CP_小die的限制 , 1.
----------------------------------------

🟩 新內容 (426 字符):
----------------------------------------
一般 Prober 限制 :Die size (含 scribe line) 必須...
----------------------------------------

📄 全部組合:
----------------------------------------
[完整合併內容]
----------------------------------------
```

## 技術實現要點
1. 修改搜索結果顯示邏輯，添加重疊檢測和分類顯示
2. 創建重疊分析器，檢測文檔塊間的重疊內容
3. 更新 UI 顯示格式，支持重疊區完整顯示，一般區截斷顯示
4. 增強詳細內容查看功能，提供三部分視圖
5. 確保虛擬環境中的測試和驗證

## 涉及文件
- `src/ui/ui_search/display.py` - 搜索結果顯示邏輯
- `src/ui/ui_search/handlers.py` - 詳細內容查看功能
- `src/search/engine.py` - 搜索引擎核心邏輯
- `src/search/chunk_analyzer.py` - 新建：塊重疊分析器
- `src/utils.py` - 結果格式化工具
- 相關文檔更新

## 成功標準
1. 搜索結果能正確區分和顯示重疊區和一般區
2. 重疊區完整顯示，一般區截斷顯示（含剩餘字數）
3. 詳細內容查看功能完整實現
4. 所有功能在 venv 環境中測試通過
5. 更新相關技術文檔