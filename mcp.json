{"mcpServers": {"sequential-thinking": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"]}, "spec-workflow": {"type": "stdio", "command": "npx", "args": ["@pimzino/spec-workflow-mcp@latest", "/mnt/d/project/python/tongrag3"], "env": {}}, "mcp-deepwiki": {"type": "stdio", "command": "npx", "args": ["mcp-deep<PERSON><PERSON>@latest"], "env": {}}, "Context7": {"type": "stdio", "command": "npx", "args": ["@upstash/context7-mcp"], "env": {}}, "playwright": {"type": "stdio", "command": "npx", "args": ["@executeautomation/playwright-mcp-server"], "env": {}}}}