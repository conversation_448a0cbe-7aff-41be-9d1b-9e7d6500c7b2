{"mcpServers": {"sequential-thinking": {"type": "stdio", "command": "npx", "args": ["@modelcontextprotocol/server-sequential-thinking"]}, "spec-workflow": {"type": "stdio", "command": "npx", "args": ["@pimzino/spec-workflow-mcp@latest", "d:\\project\\python\\tongrag3"], "env": {}}, "Context7": {"type": "stdio", "command": "npx", "args": ["@upstash/context7-mcp"], "env": {}}, "playwright": {"type": "stdio", "command": "npx", "args": ["@executeautomation/playwright-mcp-server"], "env": {}}, "serena": {"type": "stdio", "command": "uv", "args": ["run", "--directory", "D:\\project\\python\\outlook_summary\\serena", "serena", "start-mcp-server", "--context", "ide-assistant", "--project", "D:\\project\\python\\tongrag3"], "env": {}}}}