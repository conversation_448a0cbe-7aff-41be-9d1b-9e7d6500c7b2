# 向量資料庫重複ID錯誤調查結果

## 問題描述
在重建向量資料庫時出現錯誤：`Expected IDs to be unique, found 11 duplicated IDs: chunk_1, chunk_5, chunk_3, chunk_6, chunk_8, ..., chunk_4, chunk_2, chunk_11, chunk_9, chunk_7 in add.`

## 根本原因分析

### 1. DocumentLoader 的 chunk ID 生成邏輯 (src/document_loader.py:112)
```python
"chunk_id": i + 1,  # 每個文件都從 1 開始計算
```

**問題**：每個文件的 chunk ID 都是從 1 開始計算，導致不同文件的chunks會有相同的ID。

### 2. SearchEngine 的 add_documents 方法 (src/search/engine.py:152)
```python
ids = [doc.get("id", f"doc_{i}") for i, doc in enumerate(documents)]
```

**問題**：這裡使用 `doc.get("id")` 但 chunks 字典中的鍵是 `"chunk_id"` 不是 `"id"`，所以會fallback到 `f"doc_{i}"`，但在批量處理多個文件時仍會產生重複。

### 3. 數據流分析
1. `DocumentLoader.process_documents()` 處理多個文件
2. 每個文件的chunks都有 `chunk_id: 1, 2, 3...`
3. 所有chunks合併到 `all_chunks` 列表
4. `SearchEngine.add_documents(all_chunks)` 嘗試添加，但ID重複

## 修復方案
需要修改 DocumentLoader 中的 chunk ID 生成邏輯，確保全局唯一性，或修改 SearchEngine 的 ID 提取邏輯來處理 chunk_id 鍵。

## 影響範圍
- 影響所有多文件的向量資料庫重建操作
- 不影響單文件導入
- 導致重建操作完全失敗