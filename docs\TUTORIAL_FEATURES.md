# TongRAG3 教學功能說明

## 概述
已成功為 TongRAG3 文檔檢索系統添加詳細的教學說明和範例功能，提供使用者完整的學習指導。

## 新增功能

### 主選單新增選項
- **選項 9**: 查看教學說明和範例
- 支援 0-9 的選擇範圍
- 保持原有的 colorama 彩色輸出風格

### 教學內容涵蓋

#### 1. 語義搜索教學
- **什麼是語義搜索**：使用 AI 理解查詢意圖
- **適用場景**：概念性查詢、主題探索、問題解答
- **實用範例**：
  - "如何優化程式效能" → 找到效能優化相關內容
  - "資料庫連線問題" → 找到連線錯誤處理方案
  - "機器學習入門" → 找到 AI 相關概念
- **使用技巧**：自然語言描述、問句形式、包含上下文

#### 2. 關鍵詞搜索教學
- **什麼是關鍵詞搜索**：精確匹配特定詞語
- **適用場景**：特定術語、專有名詞、錯誤訊息、特定數值
- **實用範例**：
  - "ChromaDB" → 精確搜索相關文檔
  - "def search_documents" → 找到函式定義
  - "HTTP 500" → 搜索錯誤處理內容
- **注意事項**：大小寫敏感、完全匹配、無語意理解

#### 3. 正則表達式搜索教學
- **什麼是正則表達式搜索**：使用模式匹配進行搜索
- **基礎語法表**：包含常用正則表達式符號說明
- **實用範例**：
  - `def \w+\(` → 搜索 Python 函式定義
  - `\b\d{3}-\d{3}-\d{4}\b` → 搜索電話號碼格式
  - `https?://\S+` → 搜索網址
  - `v\d+\.\d+\.\d+` → 搜索版本號
- **使用技巧**：從簡單開始、注意轉義字符、使用邊界符

#### 4. 混合搜索教學（推薦）
- **什麼是混合搜索**：結合語義搜索和關鍵詞搜索
- **工作原理**：4 步流程（語義理解→關鍵詞匹配→結果融合→相關性排序）
- **實用範例**：
  - 主查詢："如何解決 ChromaDB 連線問題" + 關鍵詞："ChromaDB"
  - 主查詢："提升系統效能的方法" + 關鍵詞："performance optimization"
- **搜索策略**：廣泛搜索、精確搜索、平衡搜索

#### 5. 元數據篩選搜索教學
- **什麼是元數據篩選**：根據文檔屬性進行篩選
- **可篩選類型**：檔案名稱、路徑、時間、大小、類型、文檔塊資訊
- **實用範例**：
  - 查詢："安裝步驟" + 篩選："install" → 只在安裝文檔中搜索
  - 查詢："API 使用方法" + 篩選："README" → 只在說明文件中搜索
- **常用篩選模式**：檔案類型、功能分類、專案模組、文檔性質

#### 6. 系統狀態說明
- **功能用途**：檢視系統運行狀態、監控資料庫連線
- **狀態資訊**：集合資訊、文檔統計、文檔列表、重疊分析
- **使用場景**：系統診斷、內容管理、品質檢查、故障排除
- **狀態指示**：🟢正常、🟡警告、🔴錯誤等視覺化指示

#### 7. 導出功能說明
- **支援格式**：JSON（完整資訊）、CSV（表格格式）、TXT（純文字）
- **導出內容**：搜索結果、相關度分數、來源資訊、元數據
- **使用場景**：研究記錄、報告製作、資料分析、團隊分享
- **操作流程**：5 步完整流程說明

#### 8. 重建資料庫說明
- **何時重建**：資料庫損壞、文檔更新、設定變更、效能問題
- **重建流程**：9 步詳細流程（檢查→確認→選擇→清空→掃描→處理→生成→導入→驗證）
- **文檔處理**：支援格式、分割策略、重疊處理、向量化
- **使用場景**：首次設定、內容更新、故障恢復、效能優化

#### 9. 搜索技巧總覽
- **搜索方式選擇指南**：根據查詢類型選擇最適合的方法
- **提升搜索效果的技巧**：明確意圖、適當詞彙、結合使用、調整關鍵詞
- **搜索策略建議**：廣泛到具體、多角度搜索、組合使用、迭代優化
- **查詢優化範例**：實際的改進前後對比
- **結果解讀技巧**：關注相關度分數、查看來源資訊

## 技術特點

### 使用者體驗
- **分頁顯示**：長內容自動分頁，提升閱讀體驗
- **彩色輸出**：保持原有 colorama 風格，視覺化區分不同內容類型
- **導航便利**：支援返回教學選單或直接返回主選單
- **繁體中文**：所有內容都使用繁體中文，符合在地化需求

### 內容組織
- **結構化**：每個教學都包含概念說明、適用場景、實用範例、使用技巧
- **實用性**：提供大量實際使用範例和具體操作指導
- **全面性**：涵蓋系統的所有主要功能和使用場景
- **進階技巧**：包含最佳實踐建議和效能優化建議

### 程式碼整合
- **無破壞性修改**：完全保留原有功能，只新增教學功能
- **模組化設計**：每個教學功能都是獨立方法，便於維護
- **錯誤處理**：包含完整的異常處理和使用者友善的錯誤提示
- **向下相容**：不影響現有使用者的操作習慣

## 使用方法

1. **啟動系統**
   ```bash
   python main.py
   # 或
   python3 main.py
   ```

2. **進入教學模式**
   - 在主選單選擇 `9. 查看教學說明和範例`

3. **選擇學習主題**
   - 選擇 1-9 對應不同的搜索功能和系統說明
   - 選擇 0 返回主選單

4. **瀏覽教學內容**
   - 內容會自動分頁顯示
   - 按 Enter 返回教學選單
   - 輸入 'm' 直接返回主選單

## 檔案修改說明

### 主要修改檔案
- **`src/ui.py`**: 主要修改檔案，新增約 1000 行教學相關程式碼

### 新增方法
- `show_tutorial()`: 教學主選單
- `show_semantic_search_tutorial()`: 語義搜索教學
- `show_keyword_search_tutorial()`: 關鍵詞搜索教學
- `show_regex_search_tutorial()`: 正則表達式搜索教學
- `show_hybrid_search_tutorial()`: 混合搜索教學
- `show_metadata_search_tutorial()`: 元數據篩選搜索教學
- `show_system_status_tutorial()`: 系統狀態說明
- `show_export_tutorial()`: 導出功能說明
- `show_rebuild_tutorial()`: 重建資料庫說明
- `show_search_tips_overview()`: 搜索技巧總覽
- `paginated_display()`: 分頁顯示通用方法

### 修改的現有方法
- `display_menu()`: 新增選項 9
- `get_search_mode()`: 支援 0-9 選擇範圍
- `run_interactive_session()`: 新增教學模式處理邏輯

## 品質保證

### 語法檢查
- ✅ Python 語法檢查通過
- ✅ 所有方法定義完整
- ✅ 選單整合檢查通過

### 功能完整性
- ✅ 8 個主要搜索功能教學
- ✅ 系統管理功能說明
- ✅ 實用範例和技巧指導
- ✅ 完整的使用者界面整合

### 使用者體驗
- ✅ 繁體中文界面
- ✅ 彩色輸出視覺化
- ✅ 分頁顯示長內容
- ✅ 直觀的導航系統

這個教學系統將大幅提升 TongRAG3 的易用性，幫助使用者更好地理解和使用各種搜索功能。

---

# 搜索結果顯示優化功能（最新更新）

## 概述
最新為 TongRAG3 搜索結果顯示系統新增了三項重要的使用體驗優化功能，讓使用者能夠更直觀地了解搜索結果詳情並靈活查看完整內容。

## 新增功能詳情

### 1. 剩餘字數顯示功能
**功能描述**: 在搜索結果預覽後自動顯示剩餘字數信息
- **顯示格式**: "（還有 XXX 個字）"
- **實現位置**: `src/utils.py` 的 `truncate_text_with_count()` 函數
- **計算邏輯**: 動態計算原文長度與預覽文字的差值
- **使用效果**: 幫助使用者快速評估完整內容的豐富程度

**範例顯示**:
```
內容預覽: Friday, October 18, 2024 9:20 AM...（還有 376 個字）
```

### 2. 動態完整內容查看功能
**功能描述**: 根據當前頁面實際結果數量動態顯示範圍提示
- **動態範圍**: 支援輸入 1-N 直接查看指定編號的完整內容（N為當前頁結果數）
- **智能提示**: "輸入 1-N 查看指定編號的完整內容"會根據實際結果數量調整
- **即時查看**: 輸入編號後立即顯示該結果的完整內容，無需額外導航
- **返回便利**: 查看完整內容後可直接返回搜索結果列表

**範例顯示**:
```
============================================================
輸入 1-5 查看指定編號的完整內容
p) 上一頁 | n) 下一頁 | r) 返回選單
============================================================
```

### 3. 配置化頁面管理功能
**功能描述**: 新增頁面大小配置項，支援環境變數覆蓋
- **配置項名稱**: `PAGE_SIZE`
- **預設值**: 5（每頁顯示 5 個搜索結果）
- **環境變數**: 可通過 `PAGE_SIZE=10` 環境變數自訂
- **配置位置**: `src/config.py`
- **驗證機制**: 包含在 `validate_config()` 函數中進行有效性檢查
- **取得方式**: 透過 `get_config()` 函數統一管理

**使用範例**:
```bash
# 設定每頁顯示10個結果
export PAGE_SIZE=10
python main.py
```

## 技術實現細節

### 修改的檔案和函數

#### `src/config.py`
- **新增配置項**: `PAGE_SIZE: int = int(os.getenv("PAGE_SIZE", "5"))`
- **更新驗證函數**: `validate_config()` 新增 `PAGE_SIZE > 0` 檢查
- **更新取得函數**: `get_config()` 新增 `"page_size": PAGE_SIZE` 回傳

#### `src/utils.py`
- **新增函數**: `truncate_text_with_count(text: str, max_length: int = 200) -> tuple`
  - 回傳值: `(截斷後的文本, 剩餘字數)`
  - 智能計算: 排除 "..." 符號的實際剩餘字數
- **更新函數**: `format_results()` 新增剩餘字數和完整內容的處理
  - 新增欄位: `"remaining_chars"`, `"full_text"`
  - 使用新函數: 呼叫 `truncate_text_with_count()` 取得預覽和剩餘字數

#### `src/ui/ui_base.py`
- **導入配置**: 從 `config` 模組導入 `PAGE_SIZE`
- **使用配置**: 所有分頁邏輯使用 `PAGE_SIZE` 替代硬編碼值

#### `src/ui/ui_search.py`
- **更新 `display_single_result()`**: 
  - 顯示剩餘字數: `if result.get('remaining_chars', 0) > 0:`
  - 格式化顯示: `（還有 {remaining_chars} 個字）`
- **更新 `display_paginated_results()`**: 
  - 動態範圍計算: `end_num = start_idx + result_count`
  - 智能提示生成: 根據 `result_count` 顯示正確的範圍
  - 編號驗證: 確保輸入編號在有效範圍內
- **新增 `show_full_content()`**: 
  - 完整內容顯示: 顯示 `result.get('full_text', result.get('text', ''))`
  - 美化輸出: 包含標題、來源、相關度分數等完整資訊
  - 使用者友善: 提供清晰的分隔線和返回提示

## 功能優勢和效益

### 使用體驗改善
1. **資訊透明化**: 剩餘字數顯示讓使用者預估內容豐富程度
2. **操作直覺化**: 直接輸入數字查看完整內容，減少操作步驟
3. **範圍明確化**: 動態顯示有效編號範圍，避免無效輸入
4. **配置靈活化**: 支援環境變數調整頁面大小，適應不同使用場景

### 技術架構改善
1. **模組化設計**: 新功能完全向下相容，不破壞現有邏輯
2. **配置統一化**: 所有分頁相關設定集中管理
3. **函數專一化**: `truncate_text_with_count()` 專門處理文字截斷和計數
4. **錯誤防護**: 完整的輸入驗證和錯誤處理機制

### 效能優化
1. **記憶體效率**: 在 `format_results()` 中一次性處理，避免重複計算
2. **顯示效率**: 預先計算所有顯示所需的資料，減少即時處理
3. **配置效率**: 環境變數一次載入，全域使用

## 向下相容性

### 現有功能保持不變
- ✅ 所有原有的搜索功能正常運作
- ✅ 原有的分頁邏輯保持一致
- ✅ 原有的操作指令（p, n, r）繼續有效
- ✅ 原有的顏色和格式樣式保持不變

### 設定向下相容
- ✅ 未設定環境變數時使用預設值 `PAGE_SIZE=5`
- ✅ 舊有的配置檔案無需修改
- ✅ 所有現有的搜索引擎介面保持一致

## 使用指南

### 基本使用
1. **查看剩餘字數**: 搜索結果會自動顯示剩餘字數
2. **查看完整內容**: 在搜索結果頁面直接輸入結果編號（如: 1, 2, 3...）
3. **自訂頁面大小**: 設定環境變數 `PAGE_SIZE=數字` 後啟動系統

### 進階使用
1. **批次查看**: 可連續輸入不同編號查看多個結果的完整內容
2. **靈活配置**: 根據螢幕大小和使用習慣調整 `PAGE_SIZE`
3. **效能調校**: 較大的 `PAGE_SIZE` 減少翻頁次數，較小的值提升載入速度

### 操作流程範例
```
1. 執行搜索（任何類型）
2. 查看結果預覽和剩餘字數
3. 輸入感興趣的結果編號（如: 2）
4. 查看完整內容
5. 按 Enter 返回結果列表
6. 繼續查看其他結果或進行其他操作
```

## 品質保證

### 程式碼品質
- ✅ 完整的型別提示（Type Hints）
- ✅ 詳細的函數文檔字串
- ✅ 完善的錯誤處理機制
- ✅ 一致的程式碼風格

### 功能測試
- ✅ 剩餘字數計算準確性測試
- ✅ 動態範圍顯示正確性測試
- ✅ 配置載入和驗證測試
- ✅ 邊界條件和錯誤情況測試

### 使用者體驗測試
- ✅ 各種長度文本的顯示測試
- ✅ 不同 `PAGE_SIZE` 設定下的行為測試
- ✅ 多種搜索類型的整合測試
- ✅ 各種使用者輸入情境的測試

這些優化功能顯著提升了 TongRAG3 搜索結果的可用性和使用體驗，使系統更加人性化和高效。