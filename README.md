# TongRAG3 🔍
> 基於 ChromaDB 的智能結構化文檔檢索系統

[![Python Version](https://img.shields.io/badge/python-3.8%2B-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Status](https://img.shields.io/badge/status-stable-brightgreen.svg)]()

## 📖 專案簡介

TongRAG3 是一個功能強大且易於使用的智能文檔檢索系統，專門設計用於處理繁體中文文檔和提供直觀的搜索體驗。系統採用先進的向量搜索技術與傳統關鍵詞搜索相結合，提供精準且全面的文檔檢索能力。

### ✨ 核心特色

- **🧠 多模式搜索引擎**
  - 語義搜索：基於 AI 的語義理解搜索
  - 關鍵詞搜索：精確匹配特定詞語
  - 正則表達式搜索：支援複雜模式匹配
  - 混合搜索：結合語義與關鍵詞的智能搜索（推薦）
  - 元數據篩選：根據文檔屬性進行精確篩選
  - **🔥 語義搜索（含重排序）**：使用 BGE 重排序模型優化結果質量
  - **⚖️ 對比模式**：並排對比不同搜索方法和重排序效果
  - **⚡ 模型預載入**：消除首次搜索延遲，提升使用者體驗

- **🎨 友好的使用者體驗**
  - 繁體中文全面支援
  - 彩色命令行界面，視覺化操作體驗
  - 智能分頁顯示，支援自定義頁面大小
  - 剩餘字數顯示，快速評估內容豐富度
  - 一鍵查看完整內容，簡化操作流程
  - **🔄 重排序最佳化**：自動使用 BGE 重排序模型改善搜索品質
  - **📊 結果對比分析**：視覺化並排對比原始與重排序結果

- **📚 智能文檔處理**
  - 支援多種格式：Markdown、PDF、DOCX、TXT
  - 智能分塊處理，保持語義完整性
  - 自動元數據提取和管理
  - 增量更新和批量導入

- **🔧 靈活的配置管理**
  - 環境變數支援，適應不同使用場景
  - 可配置的搜索參數和顯示選項
  - 完整的系統狀態監控和診斷

## 🚀 快速開始

### 系統要求

- **Python 版本**：3.8 或更高版本
- **作業系統**：Windows、macOS、Linux（包含 WSL）
- **記憶體**：建議至少 4GB RAM
- **磁碟空間**：至少 2GB 可用空間

### 安裝步驟

1. **克隆專案**
```bash
git clone <repository-url>
cd tongrag3
```

2. **建立虛擬環境**
```bash
python -m venv venv

# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

3. **安裝依賴套件**
```bash
pip install -r requirements.txt
```

4. **準備文檔資料**
```bash
# 將您的文檔放置在 doc/ 目錄下
mkdir -p doc
# 複製您的 .md、.pdf、.docx、.txt 文件到 doc/ 目錄
```

5. **啟動系統**
```bash
python main.py
```

### 首次使用

1. **重建資料庫**：首次啟動時，選擇選項 `8. 重建資料庫` 來導入文檔
2. **查看教學**：選擇選項 `9. 查看教學說明和範例` 了解各種搜索功能
3. **開始搜索**：嘗試不同的搜索模式來體驗系統功能

## 🛠 主要功能說明

### 1. 語義搜索
使用 AI 理解查詢意圖，適合概念性查詢和問題解答。

```
範例查詢：
- "如何優化程式效能"
- "資料庫連線問題解決方案" 
- "機器學習入門概念"
```

### 2. 混合搜索（推薦）
結合語義理解和關鍵詞匹配，提供最佳搜索體驗。

```
使用方式：
主查詢："提升系統效能的方法"
關鍵詞："performance optimization"
```

### 3. 重排序功能（推薦）
使用先進的 BGE 重排序模型優化搜索結果質量。

```
選擇方式：主選單選項 6. 語義搜索（含重排序）

功能特色：
- 🤖 智能重排序：使用 BGE 重排序模型自動優化結果順序
- 📈 品質提升：顯著改善搜索結果相關性和準確度
- 🎯 精準匹配：更好地理解查詢意圖，提供更相關的結果
- ⚡ 高效處理：毫秒級重排序處理，不影響搜索速度
```

### 4. 對比模式
並排對比不同搜索方法和重排序效果，提供詳細的性能分析。

```
啟動方式：主選單選項 7. 對比模式

對比功能：
• 搜索方法對比：語義 vs 關鍵詞 vs 混合搜索
• 重排序效果對比：原始結果 vs 重排序結果
• 批量查詢分析：多查詢性能評估
• 排名變化追蹤：視覺化顯示結果位置變化
• 改進指標計算：量化重排序效果
```

### 5. 進階功能
- **元數據篩選**：根據檔案名稱、路徑、類型進行精確篩選
- **正則表達式**：支援複雜模式匹配，如 `def \w+\(` 搜索函數定義
- **結果導出**：支援 JSON、CSV、TXT 格式導出
- **系統狀態**：完整的資料庫狀態監控和診斷
- **對比報告導出**：詳細的搜索性能分析報告

### 6. 個性化配置
```bash
# 自定義頁面大小
export PAGE_SIZE=10

# 自定義搜索結果數量  
export DEFAULT_N_RESULTS=8

# 啟動系統
python main.py
```

## 📁 專案結構

```
tongrag3/
├── main.py                    # 主程序入口
├── src/                       # 核心源代碼
│   ├── config.py             # 配置管理
│   ├── search_engine.py      # 搜索引擎核心（含重排序）
│   ├── document_loader.py    # 文檔載入處理
│   ├── utils.py              # 工具函數
│   ├── comparison.py         # 搜索對比分析
│   ├── ui.py                 # UI模組入口
│   └── ui/                   # UI模組套件
│       ├── ui_base.py        # UI基礎功能
│       ├── ui_menu.py        # 選單和導航
│       ├── ui_search.py      # 搜索結果顯示
│       ├── ui_comparison.py  # 對比功能界面
│       ├── ui_tutorials.py   # 教學文檔
│       └── ui_system.py      # 系統管理
├── doc/                      # 文檔存放目錄
├── chroma_data/              # ChromaDB 資料庫
├── logs/                     # 系統日誌
├── CHANGELOG.md              # 版本變更記錄
├── TUTORIAL_FEATURES.md      # 功能教學說明
└── README.md                 # 專案說明（本文件）
```

## 🎯 使用範例

### 基本搜索流程
```bash
1. 啟動系統：python main.py
2. 選擇搜索模式（推薦選擇混合搜索）
3. 輸入查詢內容
4. 查看搜索結果和剩餘字數提示
5. 輸入數字查看完整內容
6. 導出結果或進行新搜索
```

### 環境變數配置
```bash
# 設定配置
export PAGE_SIZE=5              # 每頁顯示結果數
export DEFAULT_N_RESULTS=10     # 預設搜索結果數量
export CHUNK_SIZE=500           # 文檔分塊大小
export COLLECTION_NAME=my_docs  # 集合名稱

# 啟動系統
python main.py
```

## 🔧 技術架構

### 核心技術棧
- **向量資料庫**：ChromaDB - 本地向量存儲和檢索
- **嵌入模型**：BAAI/bge-m3 - 多語言語義向量化
- **重排序模型**：BAAI/bge-reranker-base - 智能結果重排序
- **文檔處理**：PyPDF2、python-docx - 多格式文檔支援
- **UI 框架**：Colorama - 彩色命令行界面
- **配置管理**：環境變數 + YAML 配置
- **對比分析**：自研對比引擎 - 多維度性能評估

### 系統特性
- **模組化設計**：清晰的架構分層，便於維護和擴展
- **錯誤處理**：完善的異常處理和使用者友好的錯誤提示
- **性能優化**：智能快取和批次處理，提升響應速度
- **向下相容**：新功能不影響現有使用體驗

## 📈 性能特色

- **搜索速度**：基於向量索引的毫秒級搜索響應
- **記憶體效率**：智能快取管理，減少記憶體佔用
- **批次處理**：支援大量文檔的高效處理
- **增量更新**：僅處理新增或變更的文檔

## 🤝 貢獻指南

我們歡迎各種形式的貢獻！

### 如何貢獻
1. Fork 這個專案
2. 建立您的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的變更 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 開啟 Pull Request

### 貢獻類型
- 🐛 錯誤修復
- ✨ 新功能開發
- 📝 文檔改進
- 🎨 UI/UX 優化
- ⚡ 性能優化
- 🌐 多語言支援

## 📋 開發規劃

### 近期規劃
- [x] **重排序功能**：BGE 重排序模型集成
- [x] **對比分析**：搜索結果並排對比功能
- [ ] Web 界面開發
- [ ] 搜索歷史記錄
- [ ] 響應式設計優化
- [ ] 更多檔案格式支援
- [ ] 重排序模型自定義配置

### 長期願景
- [ ] 分散式搜索支援
- [ ] RESTful API 服務
- [ ] 多語言界面
- [ ] AI 增強搜索

## 📜 授權信息

本專案採用 MIT 授權條款。詳細信息請參閱 [LICENSE](LICENSE) 文件。

## 📞 聯絡資訊

- **專案維護者**：TongRAG3 Team
- **問題回報**：請使用 GitHub Issues
- **功能建議**：歡迎提交 Feature Request

## 🙏 致謝

感謝以下開源專案的支持：
- [ChromaDB](https://www.trychroma.com/) - 向量資料庫
- [BAAI/bge-m3](https://huggingface.co/BAAI/bge-m3) - 多語言嵌入模型  
- [Colorama](https://pypi.org/project/colorama/) - 彩色終端輸出
- [PyPDF2](https://pypi.org/project/PyPDF2/) - PDF 處理
- [python-docx](https://pypi.org/project/python-docx/) - DOCX 處理

---

## 📚 相關文檔

- [CHANGELOG.md](CHANGELOG.md) - 版本變更記錄
- [TUTORIAL_FEATURES.md](TUTORIAL_FEATURES.md) - 功能教學說明

---

**最後更新時間**：2025-08-29  
**版本**：v0.2.0  
**狀態**：穩定版本（含重排序和對比功能）

*讓 TongRAG3 成為您文檔檢索的最佳夥伴！*