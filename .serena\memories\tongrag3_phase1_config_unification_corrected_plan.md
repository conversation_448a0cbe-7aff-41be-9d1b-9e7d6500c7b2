# TongRAG3 第一階段：配置統一計劃（修正版）

## 📊 現狀分析
- ✅ 確實存在兩個 get_config() 函式
- src/config.py：原始簡單版本（50行）
- src/config/__init__.py：新的結構化版本（420行，包含向後兼容）
- 新版本已經完全兼容舊版本的所有功能

## 🎯 修正後的實施計劃

### Task 1.1: 分析和驗證 ✅
- 確認所有使用 config 的地方
- 驗證新配置系統已包含所有舊配置項
- 測試現有功能是否正常運作

### Task 1.2: 簡化配置結構
**修正重點**：不是刪除 get_config()，而是：
1. 保留 src/config.py 作為簡單的重定向
2. 在 src/config.py 中：
   - 移除重複的 get_config() 函式定義
   - 改為從 src.config 導入所有必要項目
   - 添加 deprecation 註釋說明
3. 這樣確保：
   - 舊的導入路徑仍然有效（from src.config import get_config）
   - 減少代碼重複
   - 引導開發者使用新的結構化配置

### Task 1.3: 驗證和測試
- 運行所有 **8** 個搜索模式（不只是 1-6）
- 確認配置值一致性
- 測試 UI 界面功能
- 驗證重排序功能正常

## 🚀 預期效果
- 消除函式重複定義
- 保持 100% 向後兼容
- 代碼更清晰，維護更容易
- 為後續階段的優化打好基礎

## 📋 實施順序
1. **先測試現有系統，確認基準功能**
2. **修改 src/config.py 為重定向模組**
3. **全面測試驗證**
4. **更新記憶文件記錄完成狀態**

## ✅ 驗證清單
- [ ] 語義搜索正常
- [ ] 關鍵詞搜索正常
- [ ] 正則表達式搜索正常
- [ ] 混合搜索正常
- [ ] 元數據篩選搜索正常
- [ ] 重排序搜索正常
- [ ] 對比模式正常
- [ ] 系統狀態顯示正常

這個修正計劃更準確地反映了實際情況，並提供了更安全的實施路徑。