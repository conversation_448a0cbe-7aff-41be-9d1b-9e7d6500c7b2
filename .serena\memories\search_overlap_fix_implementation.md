# 搜索重疊顯示修復實施

## 問題位置
文件：`src/utils/search.py`
行號：第100行

## 錯誤代碼
```python
new_content = metadata.get("new_content", clean_content)
```

## 修復代碼
```python
new_content = metadata.get("new_content", "")
```

## 修復原因
當 `metadata` 中沒有 `new_content` 時，不應該回退到 `clean_content`（完整文本），而應該是空字符串，這樣後續的條件檢查才能正常工作。

## 完整修復邏輯
修復後，當文檔有重疊信息但 `new_content` 為空時，會觸發第104行的條件：
```python
if not overlap_text and not new_content and has_overlap:
```

然後在第106-109行正確計算重疊和新內容：
```python
if overlap_size > 0 and len(clean_content) >= overlap_size:
    overlap_text = clean_content[:overlap_size]
    new_content = clean_content[overlap_size:]
    overlap_percentage = (overlap_size / len(clean_content)) * 100
```

這將確保搜索結果顯示與文檔瀏覽器一致。