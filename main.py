#!/usr/bin/env python3
"""
TongRAG3 - 智能文檔搜索和檢索系統
主程序入口點

作為 Backend-Architect 設計的文檔檢索系統主程序，提供完整的系統初始化、
健康檢查、虛擬環境檢測和命令行介面功能。

功能：
- 系統健康檢查和初始化
- 虛擬環境檢測和驗證
- 智能文檔導入管理
- 命令行參數處理
- 錯誤處理和資源清理
- 與 src 模塊的完整整合
"""

import sys
import os
import logging
import argparse
import signal
import time
import traceback
import warnings
from pathlib import Path

# 抑制 NumPy longdouble 警告（WSL 環境常見問題）
warnings.filterwarnings("ignore", message=".*Signature.*longdouble.*", category=UserWarning)
from typing import Optional, Dict, Any

# 導入嵌入模型預載入功能
try:
    from src.embeddings import preload_embedding_model
except ImportError:
    def preload_embedding_model():
        return False

# 添加項目根目錄到 Python 路径
PROJECT_ROOT = Path(__file__).parent.absolute()
sys.path.insert(0, str(PROJECT_ROOT))

# 項目元數據
__version__ = "0.2.0"
__author__ = "TongRAG3 Team"
__description__ = "智能文檔搜索和檢索系統"


def setup_logging(debug: bool = False) -> logging.Logger:
    """
    設置日志配置
    
    Args:
        debug: 是否開啟調試模式
        
    Returns:
        配置好的 logger 對象
    """
    log_level = logging.DEBUG if debug else logging.INFO
    
    # 創建 logs 目錄
    log_dir = PROJECT_ROOT / "logs"
    log_dir.mkdir(exist_ok=True)
    
    # 配置日誌格式
    formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    # 設置根 logger
    logger = logging.getLogger('tongrag3')
    logger.setLevel(log_level)
    
    # 清除現有的處理器
    logger.handlers.clear()
    
    # 控制台處理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)
    
    # 文件處理器
    log_file = log_dir / "tongrag3.log"
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setLevel(logging.INFO)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)
    
    return logger


def check_virtual_environment() -> Dict[str, Any]:
    """
    檢測並驗證虛擬環境
    
    Returns:
        環境檢測結果字典
    """
    env_info = {
        'in_virtualenv': False,
        'venv_path': None,
        'python_executable': sys.executable,
        'python_version': sys.version,
        'site_packages': [],
        'warnings': [],
        'errors': []
    }
    
    # 檢查是否在虛擬環境中
    if hasattr(sys, 'real_prefix') or (
        hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
    ):
        env_info['in_virtualenv'] = True
        env_info['venv_path'] = sys.prefix
    
    # 檢查項目虛擬環境
    project_venv = PROJECT_ROOT / "venv"
    if project_venv.exists():
        if not env_info['in_virtualenv']:
            env_info['warnings'].append(f"找到項目虛擬環境 {project_venv}，但當前未激活")
        elif str(project_venv.absolute()) not in sys.prefix:
            env_info['warnings'].append(f"激活的虛擬環境與項目虛擬環境不匹配")
    else:
        env_info['warnings'].append("未找到項目虛擬環境目录 'venv'")
    
    # 獲取 site-packages 信息
    import site
    env_info['site_packages'] = site.getsitepackages()
    
    return env_info


def check_dependencies() -> Dict[str, Any]:
    """
    檢查項目依賴
    
    Returns:
        依賴檢查結果
    """
    # 分類依賴：必需和可選
    required_dependencies = {
        'chromadb': 'ChromaDB 向量數據庫',
        'pandas': '數據處理',
        'numpy': '數值計算',
        'requests': 'HTTP 請求',
    }
    
    optional_dependencies = {
        'colorama': '終端顏色支持',
        'python-docx': 'Word 文檔處理',
        'PyPDF2': 'PDF 文檔處理',
    }
    
    builtin_modules = {
        'pathlib': 'path 處理 (內建)',
        'typing': '類型提示 (內建)'
    }
    
    result = {
        'available': {},
        'missing': {},
        'optional_missing': {},
        'errors': []
    }
    
    # 檢查內建模塊
    for package, description in builtin_modules.items():
        result['available'][package] = {
            'description': description,
            'version': 'built-in',
            'status': 'ok',
            'required': True
        }
    
    # 檢查必需依賴
    for package, description in required_dependencies.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            result['available'][package] = {
                'description': description,
                'version': version,
                'status': 'ok',
                'required': True
            }
        except ImportError:
            result['missing'][package] = {
                'description': description,
                'status': 'missing',
                'required': True
            }
        except Exception as e:
            result['errors'].append(f"檢查 {package} 時發生錯誤: {e}")
    
    # 檢查可選依賴
    for package, description in optional_dependencies.items():
        try:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            result['available'][package] = {
                'description': description,
                'version': version,
                'status': 'ok',
                'required': False
            }
        except ImportError:
            result['optional_missing'][package] = {
                'description': description,
                'status': 'missing',
                'required': False
            }
        except Exception as e:
            result['errors'].append(f"檢查 {package} 時發生錯誤: {e}")
    
    return result


def health_check() -> Dict[str, Any]:
    """
    執行系統健康檢查
    
    Returns:
        健康檢查結果
    """
    health = {
        'overall_status': 'unknown',
        'timestamp': time.strftime('%Y-%m-%d %H:%M:%S'),
        'python': {
            'version': sys.version,
            'executable': sys.executable,
            'platform': sys.platform
        },
        'environment': check_virtual_environment(),
        'dependencies': check_dependencies(),
        'directories': {},
        'config': {},
        'warnings': [],
        'errors': []
    }
    
    # 檢查項目目录結構
    required_dirs = ['src', 'doc', 'chroma_data']
    optional_dirs = ['logs', 'venv', 'tests']
    
    for dir_name in required_dirs + optional_dirs:
        dir_path = PROJECT_ROOT / dir_name
        is_required = dir_name in required_dirs
        
        health['directories'][dir_name] = {
            'path': str(dir_path),
            'exists': dir_path.exists(),
            'required': is_required,
            'readable': dir_path.exists() and os.access(dir_path, os.R_OK),
            'writable': dir_path.exists() and os.access(dir_path, os.W_OK)
        }
        
        if is_required and not dir_path.exists():
            health['errors'].append(f"必需目錄不存在: {dir_name}")
    
    # 檢查源碼模塊
    src_modules = ['config.py', 'utils.py', 'document_loader.py', 'search_engine.py', 'ui.py']
    for module in src_modules:
        module_path = PROJECT_ROOT / 'src' / module
        health['directories'][f'src/{module}'] = {
            'path': str(module_path),
            'exists': module_path.exists(),
            'required': True,
            'readable': module_path.exists() and os.access(module_path, os.R_OK)
        }
        
        if not module_path.exists():
            health['errors'].append(f"核心模塊不存在: src/{module}")
    
    # 嘗試導入和驗證配置
    try:
        from src.config import validate_config, get_config
        if validate_config():
            health['config']['status'] = 'valid'
            health['config']['settings'] = get_config()
        else:
            health['config']['status'] = 'invalid'
            health['errors'].append("配置驗證失敗")
    except Exception as e:
        health['config']['status'] = 'error'
        health['errors'].append(f"載入配置時發生錯誤: {e}")
    
    # 檢查 ChromaDB 連接（可選）
    try:
        from src.search_engine import ChromaSearchEngine
        search_engine = ChromaSearchEngine()
        # 嘗試連接但不執行操作
        health['services'] = {'chromadb': 'connection_available'}
    except Exception as e:
        health['warnings'].append(f"ChromaDB 連接檢查失敗: {e}")
        health['services'] = {'chromadb': 'connection_failed'}
    
    # 計算整體狀態
    if health['errors']:
        health['overall_status'] = 'error'
    elif health['warnings'] or health['dependencies']['missing']:
        health['overall_status'] = 'warning'
    else:
        health['overall_status'] = 'healthy'
    
    return health


def convert_model_to_onnx(logger: logging.Logger) -> bool:
    """
    轉換 BGE 模型到 ONNX 格式
    
    Args:
        logger: 日志記錄器
        
    Returns:
        bool: 轉換是否成功
    """
    try:
        print("🔄 開始轉換 BGE 模型到 ONNX 格式...")
        print("   這可能需要幾分鐘時間，請耐心等待...")
        
        # 導入轉換腳本
        from convert_to_onnx import download_and_convert_model, verify_onnx_model
        
        # 執行轉換
        model_name = "BAAI/bge-reranker-base"
        output_dir = "./models/bge-reranker-base-onnx"
        
        logger.info(f"轉換模型: {model_name} -> {output_dir}")
        
        success = download_and_convert_model(
            model_name=model_name,
            output_dir=output_dir,
            max_length=512,
            batch_size=1
        )
        
        if success:
            logger.info("模型轉換成功，開始驗證...")
            print("✅ 模型轉換成功，正在驗證...")
            
            if verify_onnx_model(output_dir):
                print("✅ ONNX 模型驗證成功！")
                print(f"📁 模型保存位置: {output_dir}")
                print("🚀 現在可以使用 --use-onnx 選項來啟用 ONNX 優化")
                logger.info("ONNX 模型轉換和驗證完成")
                return True
            else:
                print("❌ ONNX 模型驗證失敗")
                logger.error("ONNX 模型驗證失敗")
                return False
        else:
            print("❌ 模型轉換失敗")
            logger.error("模型轉換失敗")
            return False
            
    except Exception as e:
        logger.error(f"ONNX 轉換過程中發生錯誤: {str(e)}")
        print(f"❌ 轉換錯誤: {str(e)}")
        return False


def initialize_system(logger: logging.Logger, use_onnx: bool = False, onnx_threads: int = None) -> bool:
    """
    初始化系統
    
    Args:
        logger: 日誌記錄器
        
    Returns:
        是否初始化成功
    """
    try:
        logger.info("開始初始化 TongRAG3 系統...")
        
        # 創建必要目錄
        required_dirs = [
            PROJECT_ROOT / "logs",
            PROJECT_ROOT / "chroma_data",
            PROJECT_ROOT / "doc"
        ]
        
        for dir_path in required_dirs:
            if not dir_path.exists():
                dir_path.mkdir(parents=True, exist_ok=True)
                logger.info(f"創建目錄: {dir_path}")
        
        # 驗證核心模塊
        try:
            from src import config, utils, search_engine, ui
            logger.info("核心模塊導入成功")
        except ImportError as e:
            logger.error(f"核心模塊導入失敗: {e}")
            return False
        
        # 驗證配置
        try:
            from src.config import validate_config
            if not validate_config():
                logger.error("系統配置驗證失敗")
                return False
            logger.info("系統配置驗證成功")
        except Exception as e:
            logger.error(f"配置驗證錯誤: {e}")
            return False
        
        # 初始化 Reranker（BGE 或 ONNX）
        try:
            from src.reranker.manager import reranker_manager
            
            if use_onnx:
                # 嘗試使用 ONNX Runtime 優化版本
                try:
                    from src.reranker import is_onnx_available, ONNXReranker
                    
                    if not is_onnx_available():
                        raise ImportError("ONNX Runtime 依賴不可用")
                    
                    # 檢查 ONNX 模型是否存在
                    onnx_model_path = Path("./models/bge-reranker-base-onnx")
                    if not onnx_model_path.exists() or not (onnx_model_path / "model.onnx").exists():
                        print("❌ ONNX 模型不存在，請先運行：python main.py --convert-to-onnx")
                        logger.error("ONNX 模型文件不存在，需要先轉換")
                        raise FileNotFoundError("ONNX 模型文件不存在")
                    
                    print("🚀 正在載入 ONNX 優化的 BGE-Reranker...")
                    logger.info("正在初始化 ONNX Reranker...")
                    
                    onnx_config = {
                        "model_path": str(onnx_model_path),
                        "max_length": 512,
                        "batch_size": 16,
                        "num_threads": onnx_threads,
                        "optimize_for_cpu": True
                    }
                    
                    success = reranker_manager.register_reranker(
                        name="onnx-bge-reranker",
                        reranker_class=ONNXReranker,
                        config=onnx_config,
                        set_as_default=True
                    )
                    
                    if success:
                        logger.info("✅ ONNX Reranker 初始化成功")
                        print(f"✅ ONNX BGE-Reranker 載入完成！")
                        if onnx_threads:
                            print(f"   使用 {onnx_threads} 個 CPU 線程")
                        print("⚡ 優化重排序功能已啟用（預期 2-3x 速度提升）")
                    else:
                        raise Exception("ONNX Reranker 註冊失敗")
                        
                except Exception as onnx_error:
                    logger.warning(f"ONNX Reranker 初始化失敗: {onnx_error}")
                    print(f"⚠️  ONNX 版本載入失敗: {onnx_error}")
                    print("🔄 回退到標準 PyTorch 版本...")
                    use_onnx = False  # 回退到標準版本
            
            if not use_onnx:
                # 使用標準 PyTorch BGE Reranker
                from src.reranker.bge_reranker import BGEReranker
                
                print("🔄 正在載入標準 BGE-Reranker 模型，首次使用可能需要2-3分鐘下載...")
                logger.info("正在初始化標準 BGE-Reranker...")
                
                success = reranker_manager.register_reranker(
                    name="bge-reranker-v2-m3",
                    reranker_class=BGEReranker,
                    config={
                        "model_name": "BAAI/bge-reranker-base",
                        "device": "auto",
                        "max_length": 512,
                        "batch_size": 32,
                        "use_fp16": True
                    },
                    set_as_default=True
                )
                
                if success:
                    logger.info("✅ 標準 BGE-Reranker 初始化成功")
                    print("✅ BGE-Reranker 載入完成！重排序功能已啟用")
                else:
                    logger.warning("❌ BGE-Reranker 初始化失敗，重排序功能將不可用")
                    print("⚠️  BGE-Reranker 載入失敗，重排序功能將不可用")
                
        except Exception as e:
            logger.warning(f"Reranker 初始化錯誤: {e}")
            print(f"⚠️  Reranker 載入錯誤: {e}")
            logger.info("系統將在沒有重排序功能的情況下繼續運行")
        
        logger.info("TongRAG3 系統初始化完成")
        return True
        
    except Exception as e:
        logger.error(f"系統初始化失敗: {e}")
        logger.debug(traceback.format_exc())
        return False


def import_documents(doc_path: str, reimport: bool = False, logger: Optional[logging.Logger] = None) -> bool:
    """
    智能文檔導入管理
    
    Args:
        doc_path: 文檔路径
        reimport: 是否重新導入
        logger: 日誌記錄器
        
    Returns:
        是否導入成功
    """
    if logger is None:
        logger = logging.getLogger('tongrag3')
    
    try:
        from src.document_loader import DocumentLoader
        from src.search_engine import ChromaSearchEngine
        
        logger.info(f"開始導入文檔: {doc_path}")
        
        # 檢查文檔路径
        doc_path_obj = Path(doc_path)
        if not doc_path_obj.exists():
            logger.error(f"文檔路径不存在: {doc_path}")
            return False
        
        # 初始化加載器和搜索引擎
        loader = DocumentLoader()
        search_engine = ChromaSearchEngine()
        
        # 如果需要重新導入，清理現有數據
        if reimport:
            logger.info("清理現有文檔數據...")
            try:
                # 這裡可以添加清理邏輯
                pass
            except Exception as e:
                logger.warning(f"清理數據時發生錯誤: {e}")
        
        # 載入文檔
        if doc_path_obj.is_file():
            # 單個文件
            try:
                content = loader.load_document(doc_path_obj)
                chunks = loader.split_text(content, str(doc_path_obj))
                if chunks:
                    search_engine.add_documents(chunks)
                    logger.info(f"成功導入文件: {doc_path_obj.name}，產生 {len(chunks)} 個塊")
                else:
                    logger.warning(f"文件未産生任何文檔: {doc_path_obj.name}")
            except Exception as e:
                logger.error(f"處理文件 {doc_path_obj.name} 失敗: {e}")
                return False
        
        elif doc_path_obj.is_dir():
            # 目錄
            try:
                all_chunks = loader.process_documents(str(doc_path_obj))
                if all_chunks:
                    search_engine.add_documents(all_chunks)
                    logger.info(f"成功導入目錄: {doc_path_obj.name}，總共 {len(all_chunks)} 個文檔塊")
                else:
                    logger.warning(f"目錄 {doc_path_obj.name} 未產生任何文檔")
            except Exception as e:
                logger.error(f"處理目錄 {doc_path_obj.name} 失敗: {e}")
                return False
        
        return True
        
    except Exception as e:
        logger.error(f"文檔導入失敗: {e}")
        logger.debug(traceback.format_exc())
        return False


def signal_handler(signum, frame):
    """
    信號處理器，用於優雅關閉
    """
    print(f"\n收到信號 {signum}，正在關閉系統...")
    # 這裡可以添加清理邏輯
    sys.exit(0)


def print_health_report(health: Dict[str, Any]) -> None:
    """
    打印健康檢查報告
    
    Args:
        health: 健康檢查結果
    """
    print(f"\n=== TongRAG3 系統健康檢查報告 ===")
    print(f"時間: {health['timestamp']}")
    print(f"整體狀態: {health['overall_status'].upper()}")
    
    # Python 環境信息
    print(f"\n--- Python 環境 ---")
    print(f"版本: {health['python']['version'].split()[0]}")
    print(f"可執行檔: {health['python']['executable']}")
    print(f"平台: {health['python']['platform']}")
    
    # 虛擬環境信息
    env = health['environment']
    print(f"\n--- 虛擬環境 ---")
    print(f"在虛擬環境中: {'是' if env['in_virtualenv'] else '否'}")
    if env['venv_path']:
        print(f"虛擬環境路径: {env['venv_path']}")
    
    # 依賴檢查
    deps = health['dependencies']
    print(f"\n--- 依賴檢查 ---")
    print(f"可用依賴: {len(deps['available'])}")
    print(f"缺少必需依賴: {len(deps['missing'])}")
    print(f"缺少可選依賴: {len(deps.get('optional_missing', {}))}")
    
    if deps['available']:
        print("  可用:")
        for name, info in deps['available'].items():
            required_marker = " (必需)" if info.get('required', True) else " (可選)"
            print(f"    ✓ {name} ({info['version']}){required_marker}")
    
    if deps['missing']:
        print("  缺少必需:")
        for name, info in deps['missing'].items():
            print(f"    ✗ {name} - {info['description']}")
    
    if deps.get('optional_missing'):
        print("  缺少可選:")
        for name, info in deps['optional_missing'].items():
            print(f"    ⚠ {name} - {info['description']}")
    
    # 目錄檢查
    print(f"\n--- 目錄結構 ---")
    for name, info in health['directories'].items():
        status = "✓" if info['exists'] else "✗"
        required = " (必需)" if info['required'] else ""
        print(f"  {status} {name}{required}")
    
    # 配置狀態
    if 'config' in health:
        print(f"\n--- 配置狀態 ---")
        print(f"狀態: {health['config']['status']}")
    
    # 服務狀態
    if 'services' in health:
        print(f"\n--- 服務狀態 ---")
        for service, status in health['services'].items():
            print(f"  {service}: {status}")
    
    # 警告和錯誤
    if health['warnings']:
        print(f"\n--- 警告 ---")
        for warning in health['warnings']:
            print(f"  ⚠ {warning}")
    
    if health['errors']:
        print(f"\n--- 錯誤 ---")
        for error in health['errors']:
            print(f"  ✗ {error}")
    
    print(f"\n=== 報告結束 ===\n")


def main():
    """主程序入口點"""
    # 設置信號處理
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # 解析命令行參數
    parser = argparse.ArgumentParser(
        description=f"TongRAG3 v{__version__} - {__description__}",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用範例:
  %(prog)s                          # 啟動交互界面
  %(prog)s --health-check-only      # 只執行健康檢查
  %(prog)s --import doc/            # 導入文檔目錄
  %(prog)s --import file.pdf --reimport  # 重新導入文件
  %(prog)s --debug                  # 開啟調試模式
  %(prog)s --convert-to-onnx        # 轉換 BGE 模型到 ONNX 格式
  %(prog)s --use-onnx               # 使用 ONNX 優化版本（需要先轉換）
  %(prog)s --use-onnx --onnx-threads 4  # 使用 ONNX 並指定線程數
        """
    )
    
    parser.add_argument(
        '--version', 
        action='version',
        version=f'%(prog)s {__version__}'
    )
    
    parser.add_argument(
        '--debug',
        action='store_true',
        help='開啟調試模式'
    )
    
    parser.add_argument(
        '--health-check-only',
        action='store_true',
        help='只執行健康檢查，不啟動系統'
    )
    
    parser.add_argument(
        '--import',
        dest='import_path',
        metavar='PATH',
        help='導入指定路径的文檔'
    )
    
    parser.add_argument(
        '--reimport',
        action='store_true',
        help='重新導入文檔（與 --import 一起使用）'
    )
    
    parser.add_argument(
        '--config-check',
        action='store_true',
        help='檢查配置文件'
    )
    
    parser.add_argument(
        '--use-onnx',
        action='store_true',
        help='使用 ONNX Runtime 優化的 reranker（需要先轉換模型）'
    )
    
    parser.add_argument(
        '--onnx-threads',
        type=int,
        default=None,
        metavar='N',
        help='ONNX Runtime 使用的 CPU 線程數（默認自動檢測）'
    )
    
    parser.add_argument(
        '--convert-to-onnx',
        action='store_true',
        help='轉換 BGE 模型到 ONNX 格式'
    )
    
    args = parser.parse_args()
    
    # 設置日誌
    logger = setup_logging(debug=args.debug)
    
    try:
        logger.info(f"TongRAG3 v{__version__} 啟動")
        logger.debug(f"命令行參數: {vars(args)}")
        
        # 執行健康檢查
        health = health_check()
        
        if args.health_check_only:
            print_health_report(health)
            sys.exit(0 if health['overall_status'] != 'error' else 1)
        
        # 處理 ONNX 轉換請求
        if args.convert_to_onnx:
            logger.info("開始 BGE 模型轉換到 ONNX 格式...")
            success = convert_model_to_onnx(logger)
            sys.exit(0 if success else 1)
        
        # 檢查系統狀態
        if health['overall_status'] == 'error':
            logger.error("系統健康檢查失敗，請查看錯誤信息")
            print_health_report(health)
            sys.exit(1)
        
        if health['overall_status'] == 'warning':
            logger.warning("系統健康檢查有警告，建議查看詳細信息")
            if args.debug:
                print_health_report(health)
        
        # 預載入嵌入模型（如果啟用）
        preload_embedding_model()
        
        # 初始化系統
        if not initialize_system(logger, use_onnx=args.use_onnx, onnx_threads=args.onnx_threads):
            logger.error("系統初始化失敗")
            sys.exit(1)
        
        # 處理文檔導入
        if args.import_path:
            success = import_documents(
                args.import_path, 
                reimport=args.reimport,
                logger=logger
            )
            if not success:
                logger.error("文檔導入失敗")
                sys.exit(1)
            
            logger.info("文檔導入完成")
            if not args.config_check:  # 如果只是導入，則退出
                sys.exit(0)
        
        # 配置檢查
        if args.config_check:
            try:
                from src.config import get_config, validate_config
                config = get_config()
                print("\n=== 系統配置 ===")
                for key, value in config.items():
                    print(f"{key}: {value}")
                print(f"\n配置有效性: {'有效' if validate_config() else '無效'}")
                print("=== 配置結束 ===\n")
            except Exception as e:
                logger.error(f"配置檢查失敗: {e}")
                sys.exit(1)
            sys.exit(0)
        
        # 啟動主應用程序
        logger.info("啟動 TongRAG3 用戶界面...")
        try:
            from src.ui import SearchInterface
            # 創建搜索引擎實例
            from src.search_engine import ChromaSearchEngine
            search_engine = ChromaSearchEngine()
            
            # 啟用重排序功能
            if hasattr(search_engine, 'enable_reranker'):
                search_engine.enable_reranker()
                print("✅ 搜索引擎重排序功能已啟用")
            
            # 創建和啟動用戶界面
            app = SearchInterface(search_engine)
            app.run_interactive_session()
        except KeyboardInterrupt:
            logger.info("用戶中斷程序")
        except Exception as e:
            logger.error(f"應用程序運行錯誤: {e}")
            logger.debug(traceback.format_exc())
            sys.exit(1)
        
    except Exception as e:
        logger.error(f"程序執行錯誤: {e}")
        logger.debug(traceback.format_exc())
        sys.exit(1)
    
    finally:
        logger.info("TongRAG3 程序結束")


if __name__ == "__main__":
    main()