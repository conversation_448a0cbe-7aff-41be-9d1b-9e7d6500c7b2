TE部門時效性規定

1. 正式 release 需在獨立光罩批第一批量測開始後一週內或兩大批(後到後算, **最晚不超過兩個月**)。2018/01後起適用(含以下).

**第一版即為獨立光罩產品以不再改版的第一批起算.**

**2019/07後依工作紀錄規定執行。(依產品分六 or 八週)**

1. 同一版本Eng 版程式在IC未改版情況下於使用一週內或兩大批(後到後算, **最晚不超過一個月**)後，即須正式release 正式版程式。
2. 第一批獨立光罩量測後一個月或 500K(先到先算)後測試時間或良率不符規定、或非屬測試問題但未確實反映給RD者，量產維護分數的量照算但分數不計，**違反第1,2點的處置相同**。

**2019/07後依工作紀錄規定執行。**

1. 外測廠新發異常(低良率、Q fail)通知時，TE 需於 24 小時內回覆處置動作，放假日前一天請於下班前回覆。異常批需於**兩個月**處理完成(release 或轉其他權責單位負責)，如處理時效非TE所能掌握者，TE仍須善盡責任追蹤、通知並cc給主管。
2. 程式未 release 收到”工單無法帶出程式代號”通知後如超過一個月(以符合正式 release規定的最終時間起算)的仍未 release，該負責工程師所屬team 的主管則必須請全部門吃下午茶。

TE部門其他規定

(已有規定文件則不重複列舉)

1. 新洗 4 層 PCB 板如無特殊需求者，統一規定為 Top, Bottom, MidLayer1, MidLayer2。
2. 線上貨批如需改程式重測必須 cc 給 PC部門 (<EMAIL>)
3. 產品在掃溫度特性時, 如有更改測試治具時, 應先檢查在常溫時兩者的數據一致後才開始掃溫度
4. 廠內CP(CSP)時wafer map 的圈選統一請心怡設定, 獨立光罩或combo wafer 皆適用。
5. 製作 probe card 時 default 不提供 wafer, 如需提供請與主管討論。測試前再領出 wafer, 如 RD or plma 提早給請勿收下,不用時請妥善收好, **嚴禁**在 wafer 置放在 prober 內 idle….
6. RD 要求與 AE 做特性 correlation 時，請 RD 發正式 e-mail 通知TE 及AE，如有特殊需求請一併寫清楚；TE並回覆可提供 samples 的時間.
7. 當 Low yield 發生，降低良率 & EQC pass 先 pass go, fail hold 的指令只限於該貨批在假日測試且必須於收假後第一天上班日回貨的條件成立。平日則必須先確認過測試資料才能下指令，且以測試廠發的原e-mail回覆。
8. 工程或量產品驗證分析結果除了給 RD 是需要詳細的資料，其他部門的需求只需要提供結果即可，過程不贅述。如工程師個人無法判斷則交由各 team 的主管回覆。
9. 請勿將公司內部的 e-mail 直接轉給外測廠.
10. 產品負責TE的 assign必須為PLM向部門主管提出TE需求後由部門主管回覆，非經此流程的產品相關工作紀錄一律不予計分
11. correlation samples 需要驗不良品，並加註在委外測試要點內，只要是發release form 不管是建立或變更
12. 提供 CSP 的 ESD or Samples 時，須跟 plma 拿取專用的 tray 盤來裝
13. 友達(AUO)跟群創(INX) RFQ 的產品都一定要做高溫CP，測 o/s、leakage、IQ。如需要 CP trim 的則維持常溫
14. 客訴 IC 測試驗證須使用QC limit，避免改變FT trim 的 trim code。如有 NVM (EEPROM, OTP, FLASH) 的產品則須用特別程式不能重新寫入NVM.
15. WLCSP 產品且使用 pogo pin socket者如要驗證 ESD samples時請勿開啟 continue on fail，會有過熱熔球造成 socket damage 的風險.
16. 封裝工程實驗如未經主管同意逕行回覆資料確認結果者，須將此實驗的原因、實驗內容、該實驗對測試可能的影響、實際該實驗對測試的影響、測試數據比對後的差異整理成報告後於TE雙週會提出分享，該報告無計分.
17. 改 multi-sites 的測試程式後如測試程式有進版時，原本較少sites 的測試程式需一併進版，亦或是通知MIS取消舊程式。各team manage 及leader 會簽時須特別注意檢查.
18. Low-K probe card wafer 設定針壓max為 first\_contact + 40um，first contact 定義為第一針有接觸到時，否則會失去Low-K的效用.
19. 提供樣品時必須使用完整的測試程式測試, 如程式不全要測試需先 highlight給各分組主管定奪, 第一次& 有修改測試程式提供樣品時, 需將 datalog files 寄給各分組主管確認.

需額外處罰之項目列舉

(2017/11/29 主管會議定案)

1. 違反部門規定, 造成部門或公司實質 & 非實質損失者。

**“下午茶(飲料+食物)+經驗分享一次”**

**以下的額外處罰為”經驗分享一次”。經驗分享可提出之前所提且具有相當重要性的報告，照本宣科的分享不列入計算。或以一次部門飲料替代之。**

1. 違反部門規定, 有一定風險造成部門或公司實質 & 非實質損失者：(**a, b, c 為列舉範例非全部**)
2. 未核實檢查 & 附上產品測試建立時所需的資料。
3. 未正式release 或使用 ENG 程式超過規定時效一個月者(系列產品共用程式已有版本正式 release則交由主管會議認定)。
4. 獨立光罩改版紀錄未核實填寫者(已知且有處理者請補附往來e-mail核實後**第一次可免罰**)。
5. 無違反規定, 但造成部門或公司實質 & 非實質損失時, 經主管會議討論認定為**”工程師”可事先避免者**. 包含一定程度之應注意而未注意的行為或指令.
6. 測試異常暫扣的貨批(整批或不良品), 經測試廠通知後逾兩個月後仍未 release, 處理權責仍在測試部門者.(2017/9/27主管會議通過). 如跟測試廠往來時效性問題, 至少於每兩週內(<=N+14, 超過就算違規)都有追蹤處理者, 可列為除外狀況.(2017/12/13主管會議通過)
7. 合乎 release 條件之舊產品(2016/12/31前投產產品)未於收到生管未 release 通知後兩週內 release.

**以下違規者為自動晉級負責管理人員, 如超過一人則推舉一人當管理人員,其餘處罰同上。如因違規而晉級管理人員者無落實執行檢查時處罰同上。**

1. 部門規定定期檢查項目(軟、硬體等)逾期未確實執行者。
2. 設備、製具領用不依規定執行, 如填寫相關表單…等等。

公司非實質損失一般指的是客訴..etc, 而部門非實質損失則交由主管會議認定之.

備註 : 下午茶的責任分攤為**當事人 1 : 直屬主管 1 : 直屬主管之主管 : 1.(最高至部門主管)**