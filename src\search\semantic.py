"""
Semantic Search Module

This module implements semantic search functionality using vector similarity
search with ChromaDB. It provides semantic matching capabilities that understand
the meaning and context of queries beyond exact keyword matches.
"""

import logging
from typing import Dict, List, Optional, Any
from chromadb.api.models.Collection import Collection

from ..config import MAX_RESULTS
from ..utils import convert_english_to_uppercase, format_results
from .rerank_mixin import RerankerMixin

# 設置日志
logger = logging.getLogger(__name__)


class SemanticSearchMixin(RerankerMixin):
    """
    Mixin class providing semantic search functionality using vector similarity.
    
    This class implements semantic search using ChromaDB's built-in text embedding
    functionality. It converts queries to vectors and finds semantically similar
    documents using cosine similarity.
    """
    
    def semantic_search(
        self, 
        query: str, 
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        語義搜索，使用 ChromaDB 內建的文本嵌入功能進行向量相似度匹配。
        
        ChromaDB 會自動將查詢文本轉換為向量，並與文檔庫中的向量進行
        餘弦相似度比較，找到最相關的文檔。
        
        Args:
            query: 搜索查詢
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            # Ensure connection exists (this method should be provided by the main class)
            if hasattr(self, '_ensure_connection'):
                self._ensure_connection()
            
            if not query.strip():
                return []
            
            # 將英文字母轉換為大寫
            processed_query = convert_english_to_uppercase(query)
            logger.debug(f"語義搜索 - 原查詢: '{query}' -> 處理後: '{processed_query}'")
            
            n_results = min(n_results, MAX_RESULTS)
            
            # Perform semantic search using collection (should be available from main class)
            if not hasattr(self, 'collection') or self.collection is None:
                logger.error("Collection not available for semantic search")
                return []
            
            results = self.collection.query(
                query_texts=[processed_query],
                n_results=n_results
            )
            
            # Format search results (this method should be provided by the main class)
            if hasattr(self, '_format_search_results'):
                formatted_results = self._format_search_results(results)
            else:
                formatted_results = self._default_format_results(results)
            
            # Apply reranking if requested and available
            if use_reranker and self.is_reranker_enabled():
                logger.debug("Applying reranking to semantic search results")
                formatted_results = self._apply_reranking(
                    query=processed_query,
                    search_results=formatted_results,
                    reranker_name=reranker_name,
                    top_k=n_results,
                    score_threshold=score_threshold
                )
            
            logger.debug(f"語義搜索完成: 返回 {len(formatted_results)} 個結果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"語義搜索失敗: {str(e)}")
            return []
    
    def _default_format_results(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Default formatting for search results when main class method is not available.
        
        Args:
            results: Chroma query results
            
        Returns:
            Formatted results list
        """
        # 轉換為標準格式
        formatted_list = []
        if results and results.get('ids'):
            ids = results['ids'][0] if results['ids'] else []
            documents = results['documents'][0] if results.get('documents') else []
            metadatas = results['metadatas'][0] if results.get('metadatas') else []
            distances = results['distances'][0] if results.get('distances') else []
            
            for i, doc_id in enumerate(ids):
                result = {
                    "id": doc_id,
                    "text": documents[i] if i < len(documents) else "",
                    "metadata": metadatas[i] if i < len(metadatas) else {},
                    "distance": distances[i] if i < len(distances) else 0.0,
                    "source": metadatas[i].get("source", "") if i < len(metadatas) and metadatas[i] else ""
                }
                formatted_list.append(result)
        
        return format_results(formatted_list)
    
    def semantic_search_with_filters(
        self,
        query: str,
        filters: Dict[str, Any],
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        帶過濾條件的語義搜索。
        
        結合向量相似度搜索和元數據過濾，在語義匹配的基礎上
        根據文檔元數據進行進一步篩選。
        
        Args:
            query: 搜索查詢
            filters: 元數據過濾條件
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            # Ensure connection exists
            if hasattr(self, '_ensure_connection'):
                self._ensure_connection()
            
            if not query.strip():
                return []
            
            # 將英文字母轉換為大寫
            processed_query = convert_english_to_uppercase(query)
            logger.debug(f"語義過濾搜索 - 原查詢: '{query}' -> 處理後: '{processed_query}'")
            
            n_results = min(n_results, MAX_RESULTS)
            
            # Check collection availability
            if not hasattr(self, 'collection') or self.collection is None:
                logger.error("Collection not available for semantic search with filters")
                return []
            
            results = self.collection.query(
                query_texts=[processed_query],
                n_results=n_results,
                where=filters
            )
            
            # Format search results
            if hasattr(self, '_format_search_results'):
                formatted_results = self._format_search_results(results)
            else:
                formatted_results = self._default_format_results(results)
            
            # Apply reranking if requested and available
            if use_reranker and self.is_reranker_enabled():
                logger.debug("Applying reranking to semantic search with filters results")
                formatted_results = self._apply_reranking(
                    query=processed_query,
                    search_results=formatted_results,
                    reranker_name=reranker_name,
                    top_k=n_results,
                    score_threshold=score_threshold
                )
            
            logger.debug(f"語義過濾搜索完成: 返回 {len(formatted_results)} 個結果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"語義過濾搜索失敗: {str(e)}")
            return []
    
    def batch_semantic_search(
        self,
        queries: List[str],
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[List[Dict[str, Any]]]:
        """
        批量語義搜索，同時處理多個查詢。
        
        Args:
            queries: 查詢列表
            n_results: 每個查詢返回的結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱  
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表的列表
        """
        try:
            if not queries:
                return []
            
            # Process each query individually
            results = []
            for query in queries:
                query_results = self.semantic_search(
                    query=query,
                    n_results=n_results,
                    use_reranker=use_reranker,
                    reranker_name=reranker_name,
                    score_threshold=score_threshold
                )
                results.append(query_results)
            
            logger.debug(f"批量語義搜索完成: 處理 {len(queries)} 個查詢")
            return results
            
        except Exception as e:
            logger.error(f"批量語義搜索失敗: {str(e)}")
            return []
    
    def semantic_similarity_threshold_search(
        self,
        query: str,
        similarity_threshold: float = 0.7,
        max_results: int = 50,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        基於相似度閾值的語義搜索。
        
        只返回相似度高於指定閾值的結果，適用於需要高質量匹配的場景。
        
        Args:
            query: 搜索查詢
            similarity_threshold: 相似度閾值 (0.0-1.0)
            max_results: 最大結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            # Get more results initially to apply threshold filtering
            initial_results = self.semantic_search(
                query=query,
                n_results=min(max_results * 2, MAX_RESULTS),
                use_reranker=False  # Apply reranking after threshold filtering
            )
            
            # Filter by similarity threshold
            # Convert distance to similarity: similarity = 1 - distance
            filtered_results = []
            for result in initial_results:
                similarity = 1.0 - result.get("distance", 1.0)
                if similarity >= similarity_threshold:
                    result["similarity"] = similarity
                    filtered_results.append(result)
            
            # Limit to max_results
            filtered_results = filtered_results[:max_results]
            
            # Apply reranking if requested and available
            if use_reranker and self.is_reranker_enabled() and filtered_results:
                logger.debug("Applying reranking to threshold-filtered semantic search results")
                filtered_results = self._apply_reranking(
                    query=query,
                    search_results=filtered_results,
                    reranker_name=reranker_name,
                    top_k=len(filtered_results),
                    score_threshold=score_threshold
                )
            
            logger.debug(f"閾值語義搜索完成: 閾值={similarity_threshold}, 返回 {len(filtered_results)} 個結果")
            return filtered_results
            
        except Exception as e:
            logger.error(f"閾值語義搜索失敗: {str(e)}")
            return []