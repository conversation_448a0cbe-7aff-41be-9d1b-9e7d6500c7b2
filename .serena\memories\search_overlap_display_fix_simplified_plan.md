# 搜索結果重疊顯示修復計劃（簡化版）

## 問題現狀
用戶反映搜索結果中的"塊 2"仍然顯示為"🟩 獨有內容（完整）"，而不是預期的重疊分析格式（🟨 重疊部分 + 🟩 新內容）。

## 核心原則
- 不做向後兼容：舊文檔沒有重疊信息就顯示為沒有重疊
- 第一塊永遠沒有重疊：這是正確的邏輯
- 其他塊根據實際情況顯示：有重疊就顯示，沒有就不顯示

## 實施步驟

### 步驟1：修正 document_loader.py 的重疊計算邏輯
- 修改 `_split_text_into_chunks` 方法
- 第一塊 (i == 0) 永遠沒有重疊
- 其他塊根據位置計算實際重疊：`overlap_size = prev_end - start_pos`

### 步驟2：更新 utils/search.py 的結果格式化
- 修改 `format_results` 函數
- 根據 `overlap_size` 分離重疊區和新內容
- 添加 `overlap_text`, `new_content`, `overlap_percentage` 字段

### 步驟3：更新 ui/ui_search/display.py 的顯示邏輯
- 修改 `_display_overlap_sections` 方法
- 有重疊時顯示：🟨 重疊部分 + 🟩 新內容 + 完整內容
- 沒有重疊時顯示：🟩 獨有內容（完整）

## 預期結果
- 新文檔塊1：🟩 獨有內容（第一塊）
- 新文檔塊2+：🟨 重疊部分 + 🟩 新內容（如果有重疊）
- 舊文檔所有塊：🟩 獨有內容（沒有重疊信息）

## 驗證方式
在 venv 環境中進行互動式驗證，重新導入文檔後檢查搜索結果顯示格式。