# TongRAG3 ONNX Runtime 整合指南

## 概述

本指南說明如何在 TongRAG3 中使用 ONNX Runtime 優化 BGE reranker 的性能。ONNX Runtime 可以提供 2-3x 的速度提升和 30-50% 的記憶體使用減少，同時保持相同的重排序質量。

## 🚀 快速開始

### 1. 安裝依賴

```bash
pip install -r requirements.txt
```

主要新增的 ONNX 相關依賴：
- `onnxruntime>=1.16.0`
- `onnxruntime-tools>=1.7.0`
- `optimum[exporters]>=1.14.0`
- `onnx>=1.15.0`
- `psutil>=5.9.0`

### 2. 轉換模型到 ONNX 格式

```bash
python main.py --convert-to-onnx
```

或者直接使用轉換腳本：

```bash
python convert_to_onnx.py --model BAAI/bge-reranker-base --output ./models/bge-reranker-base-onnx
```

### 3. 使用 ONNX 優化版本

```bash
# 使用 ONNX 版本（自動線程數）
python main.py --use-onnx

# 指定 CPU 線程數
python main.py --use-onnx --onnx-threads 4

# 性能對比測試
python benchmark_onnx_vs_pytorch.py
```

## 📋 完整實施清單

### ✅ 已實施的功能

1. **依賴管理**
   - ✅ 更新 `requirements.txt` 添加 ONNX 相關依賴
   - ✅ 可選依賴導入，不影響現有功能

2. **模型轉換**
   - ✅ 自動模型轉換腳本 (`convert_to_onnx.py`)
   - ✅ 支持 BAAI/bge-reranker-base 模型
   - ✅ 自動驗證轉換結果
   - ✅ 模型配置保存

3. **ONNX Reranker 實現**
   - ✅ 完整的 `ONNXReranker` 類別 (`src/reranker/onnx_reranker.py`)
   - ✅ 繼承 `BaseReranker` 基類
   - ✅ 相同的 API 接口
   - ✅ 可配置的 CPU 線程數
   - ✅ 批處理支援
   - ✅ 詳細的錯誤處理和日誌記錄
   - ✅ 線程安全操作
   - ✅ 性能監控和統計

4. **系統整合**
   - ✅ `main.py` 添加 ONNX 選項
   - ✅ RerankerManager 支援 ONNX backend
   - ✅ 向後兼容性
   - ✅ 自動回退機制

5. **性能測試**
   - ✅ 完整的性能對比腳本 (`benchmark_onnx_vs_pytorch.py`)
   - ✅ 速度、記憶體使用、準確性對比
   - ✅ 多線程配置測試
   - ✅ 詳細的性能報告

6. **測試驗證**
   - ✅ 整合測試腳本 (`test_onnx_integration.py`)
   - ✅ 依賴檢查
   - ✅ 模組導入測試
   - ✅ 功能驗證

## 🏗️ 架構設計

### 核心組件

```
src/reranker/
├── base.py              # 基礎類別和數據結構
├── bge_reranker.py      # 原始 PyTorch 實現
├── onnx_reranker.py     # 新的 ONNX 實現
├── manager.py           # Reranker 管理器
├── utils.py             # 工具函數
└── __init__.py          # 模組導出和可用性檢查
```

### 關鍵特性

1. **統一接口**: ONNX 版本使用相同的 `RerankRequest`/`RerankResponse` 結構
2. **線程安全**: 支援多線程環境下的並發使用
3. **自動優化**: CPU 優化設置和自動線程數檢測
4. **錯誤處理**: 完善的異常處理和資源清理
5. **性能監控**: 內建的性能統計和監控功能

## 📊 性能預期

基於測試和 ONNX Runtime 的典型表現：

### 速度提升
- **推理速度**: 2-3x 提升（1.9秒 → 0.6-1.2秒）
- **吞吐量**: 2-3x 提升（文檔處理數/秒）
- **批處理**: 更高效的批次處理

### 記憶體優化
- **記憶體使用**: 減少 30-50%
- **GPU 記憶體**: 無需 GPU 記憶體（CPU 推理）
- **模型載入**: 更快的模型載入時間

### 準確性保證
- **相關性分數**: 與原始 PyTorch 版本差異 < 5%
- **排序一致性**: 高度一致的文檔排序
- **數值穩定性**: 經過驗證的數值計算

## 🛠️ 使用方式

### 命令行選項

```bash
# 基礎選項
python main.py --use-onnx                    # 使用 ONNX 版本
python main.py --onnx-threads 4              # 指定線程數
python main.py --convert-to-onnx             # 轉換模型

# 結合其他選項
python main.py --use-onnx --debug            # ONNX + 調試模式
python main.py --use-onnx --import doc/      # ONNX + 文檔導入
```

### 程式化使用

```python
from src.reranker import ONNXReranker, RerankRequest

# 初始化 ONNX reranker
reranker = ONNXReranker(
    model_path="./models/bge-reranker-base-onnx",
    num_threads=4,
    batch_size=16
)

# 初始化模型
if reranker.initialize():
    # 創建重排序請求
    request = RerankRequest(
        query="your search query",
        documents=[
            {"text": "document 1 content"},
            {"text": "document 2 content"}
        ]
    )
    
    # 執行重排序
    response = reranker.rerank(request)
    
    # 處理結果
    for result in response.results:
        print(f"Doc {result.document_id}: {result.score:.4f}")
    
    # 清理資源
    reranker.cleanup()
```

### RerankerManager 整合

```python
from src.reranker.manager import reranker_manager
from src.reranker import ONNXReranker

# 註冊 ONNX reranker
reranker_manager.register_reranker(
    name="onnx-bge",
    reranker_class=ONNXReranker,
    config={
        "model_path": "./models/bge-reranker-base-onnx",
        "num_threads": 4
    },
    set_as_default=True
)

# 使用管理器進行重排序
response = reranker_manager.rerank(request)
```

## 🧪 性能測試

### 運行基準測試

```bash
# 完整對比測試
python benchmark_onnx_vs_pytorch.py

# 指定參數
python benchmark_onnx_vs_pytorch.py --iterations 5 --onnx-threads 8

# 只測試 ONNX 版本
python benchmark_onnx_vs_pytorch.py --onnx-only --output onnx_results.json
```

### 測試輸出示例

```
🔥 ONNX vs PyTorch BGE Reranker 性能對比結果
============================================================

📋 測試環境:
   平台: Linux-4.4.0-26100-Microsoft
   處理器: Intel64 Family 6 Model 142 Stepping 10
   CPU 核心: 8 物理, 16 邏輯
   記憶體: 16.0 GB

⚡ PyTorch 結果:
   平均處理時間: 1.850s/query
   吞吐量: 12.3 docs/sec

🚀 ONNX 結果:
   平均處理時間: 0.680s/query
   吞吐量: 31.2 docs/sec
   使用線程數: 8

📊 性能對比:
   🏃 速度提升: 2.72x (172.1%)
   📈 吞吐量提升: 2.54x
   ⏱️  每查詢節省時間: 1170.0ms

🎯 準確性分析:
   平均相對誤差: 2.3%
   一致性狀態: good

💡 建議:
   ✅ 推薦使用 ONNX 版本
   理由: ONNX 提供 2.7x 速度提升，準確性差異 2.3%
```

## 🔧 故障排除

### 常見問題

1. **模型不存在錯誤**
   ```
   錯誤: ONNX 模型不存在
   解決: python main.py --convert-to-onnx
   ```

2. **依賴缺失**
   ```
   錯誤: ImportError: No module named 'onnxruntime'
   解決: pip install -r requirements.txt
   ```

3. **轉換失敗**
   ```
   錯誤: 模型轉換失敗
   解決: 檢查網絡連接，確保有足夠磁盤空間
   ```

4. **性能不如預期**
   ```
   解決: 調整 --onnx-threads 參數，通常設為 CPU 核心數
   ```

### 調試技巧

1. **開啟調試模式**
   ```bash
   python main.py --use-onnx --debug
   ```

2. **檢查系統兼容性**
   ```bash
   python test_onnx_integration.py
   ```

3. **驗證模型文件**
   ```bash
   python convert_to_onnx.py --verify --skip-conversion
   ```

## 📈 進階配置

### 線程數調優

```python
import multiprocessing

# 保守設置（適合共享環境）
num_threads = min(4, multiprocessing.cpu_count())

# 積極設置（適合專用環境）
num_threads = multiprocessing.cpu_count()

# 自定義設置
reranker = ONNXReranker(num_threads=num_threads)
```

### 批處理大小調優

```python
# 小批次（適合低記憶體環境）
reranker = ONNXReranker(batch_size=8)

# 大批次（適合高記憶體環境）
reranker = ONNXReranker(batch_size=32)
```

### 自定義模型路徑

```python
# 使用不同的模型路徑
reranker = ONNXReranker(
    model_path="/custom/path/to/onnx/model",
    max_length=512
)
```

## 🏁 總結

TongRAG3 的 ONNX Runtime 整合提供了：

1. **顯著的性能提升**: 2-3x 速度提升，30-50% 記憶體節省
2. **完全向後兼容**: 無縫升級，不影響現有功能
3. **生產就緒**: 完善的錯誤處理、日誌記錄和監控
4. **易於使用**: 簡單的命令行選項和程式化 API
5. **全面測試**: 完整的測試套件和性能基準

### 建議使用場景

- ✅ **生產環境**: 高併發、性能敏感的應用
- ✅ **批次處理**: 大量文檔的重排序任務
- ✅ **資源受限**: CPU 和記憶體有限的環境
- ✅ **成本優化**: 減少計算資源成本

### 開發路線圖

未來可能的改進：
- [ ] GPU 加速支援（CUDA/ROCm）
- [ ] 更多 BGE 模型變體支援
- [ ] 動態批次大小調整
- [ ] 模型量化選項
- [ ] 分散式推理支援

---

**注意**: 首次使用需要轉換模型，這個過程可能需要幾分鐘時間和網絡連接來下載原始模型。