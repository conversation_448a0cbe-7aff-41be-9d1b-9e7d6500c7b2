# TongRAG3 ONNX 功能測試任務

## 🎯 任務目標
測試 `python3 main.py --use-onnx` 的完整功能性，驗證八個搜索選項是否正常工作：

### 待測試功能列表
1. 語義搜索（向量相似度匹配）- BGE-M3模型1024維向量餘弦距離
2. 關鍵詞搜索（精確匹配）- 字符包含匹配+向量排序
3. 正則表達式搜索（模式匹配）- Regex引擎，支持通配符和複雜模式
4. 混合搜索（語義+關鍵詞，最佳效果）- 70%語義+30%關鍵詞加權融合
5. 元數據篩選搜索 - 向量搜索+結構化屬性過濾
6. 語義搜索（含重排序）- BGE重排序模型優化結果質量
7. 對比模式 - 並排對比不同搜索方法和重排序效果
8. 查看系統狀態

## 📋 測試計劃
1. 激活虛擬環境 (venv)
2. 執行 `python3 main.py --use-onnx`
3. 逐個測試所有8個功能選項
4. 記錄性能和功能完整性
5. 驗證 ONNX 優化是否生效

## 🔍 預期結果
- 所有功能正常運作
- ONNX 重排序性能提升明顯
- 對比模式顯示正確
- 系統狀態健康

## 📊 現有狀態
根據 onnx_integration_final_results 記憶：
- ONNX 整合已完成
- 性能提升 3x+ (重排序)
- 整體性能改進 50%+
- 完全向後兼容