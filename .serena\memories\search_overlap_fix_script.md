# 搜索重疊顯示修復腳本

## 修復方法
用戶需要手動修改 `src/utils/search.py` 文件的第100行：

### 修改前（錯誤）：
```python
new_content = metadata.get("new_content", clean_content)
```

### 修改後（正確）：
```python
new_content = metadata.get("new_content", "")
```

## 修復步驟
1. 打開文件：`src/utils/search.py`
2. 找到第100行
3. 將 `clean_content` 改為 `""`
4. 保存文件
5. 重新啟動程式測試

## 驗證方法
修復後，搜索結果應該顯示：
- 🟨 重疊部分 (100 字符, 19.0%)
- 🟩 新內容 (426 字符)

而不是：
- 🟩 獨有內容（完整）

## 技術原理
這個修復確保當文檔有重疊信息時，`new_content` 不會錯誤地回退到完整文本，從而讓後續的重疊檢測邏輯能夠正常工作。