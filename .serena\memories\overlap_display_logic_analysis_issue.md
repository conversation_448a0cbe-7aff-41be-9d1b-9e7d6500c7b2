# Overlap Display Logic Analysis - Root Cause Found

## The Problem
User reported inconsistency where:
1. Search results show "🟩 獨有區域（完整）" (complete unique area)
2. But document analysis clearly shows overlap exists with "🟨 重疊部分" (overlap section)
3. This creates confusion about whether search overlap and document overlap are different things

## Root Cause Analysis

### The Display Logic Issue
Looking at the code in `src/ui/ui_search/display.py` and `src/utils.py`, I found the issue:

1. **In `_display_overlap_sections()` method (line 288-310)**:
   - It checks for `overlap_preview` field to display overlap section
   - If `overlap_preview` is empty, it won't show the overlap section
   - It then shows unique content under "🟩 一般區" (general area)

2. **In `_enhance_result_with_overlap_display()` function (line 463-471)**:
   - If `overlap_content` exists, it generates `overlap_preview` (truncated to 100 chars)
   - If `overlap_content` is empty, it sets `overlap_preview = ''` and `overlap_remaining_chars = 0`

3. **The key issue**: The search results display uses `overlap_preview` for the preview display, but the full content analysis uses `overlap_info.overlap_content` directly.

### Why This Creates Inconsistency

When a document has overlap but the `overlap_content` in the search result is empty or not properly populated:
- Search preview shows: "🟩 獨有區域（完整）" because `overlap_preview` is empty
- Full document analysis shows: "🟨 重疊部分" because it finds actual overlap in the document text

### The Two Different "Overlaps"

The user's confusion is valid - there ARE two different types of overlap being detected:

1. **Search Result Overlap**: Detected during search formatting (`overlap_info.overlap_content`) 
   - This is what shows in the search preview
   - Generated by `OverlapDetector.detect_overlaps()`

2. **Document Block Overlap**: Detected during document chunk analysis
   - This is what shows in full document view
   - Generated when viewing complete document chunks with their internal overlaps

### Why Results Differ

These can differ because:
- Search result overlap detection may fail or find no overlaps between search results
- Document chunk overlap detection analyzes actual chunk boundaries and finds internal overlaps
- The search results may not include the chunks where overlap actually occurs

## Solution Required

The display logic should be consistent and clearly indicate which type of overlap is being shown. The preview should accurately reflect what will be found in the full content view.