#!/usr/bin/env python3

try:
    from huggingface_hub import list_repo_tree
    print("✅ list_repo_tree import successful")
except ImportError as e:
    print(f"❌ list_repo_tree import failed: {e}")

try:
    import sentence_transformers
    print("✅ sentence_transformers import successful")
except ImportError as e:
    print(f"❌ sentence_transformers import failed: {e}")

try:
    from src.utils import OverlapDetector
    print("✅ OverlapDetector import successful")
except ImportError as e:
    print(f"❌ OverlapDetector import failed: {e}")

print("Testing overlap detection...")
try:
    detector = OverlapDetector()
    test_results = [
        {"id": "1", "text": "這是第一個測試文檔", "metadata": {"file_name": "test.txt"}},
        {"id": "2", "text": "這是第一個測試文檔的延續", "metadata": {"file_name": "test.txt"}}
    ]
    enhanced = detector.detect_overlaps(test_results)
    print(f"✅ Overlap detection test successful: {len(enhanced)} results")
    for result in enhanced:
        print(f"  - ID: {result['id']}, has_overlap: {result.get('has_overlap', False)}")
except Exception as e:
    print(f"❌ Overlap detection test failed: {e}")
