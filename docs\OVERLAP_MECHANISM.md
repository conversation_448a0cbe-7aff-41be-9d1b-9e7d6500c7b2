# TongRAG3 搜索結果重疊機制技術文檔

> **TongRAG3 v0.2.1** - 搜索結果重疊檢測與顯示機制完整技術說明

## 📋 目錄

- [概覽](#概覽)
- [重疊機制數學原理](#重疊機制數學原理)
- [元數據結構說明](#元數據結構說明)
- [顯示格式規範](#顯示格式規範)
- [技術實現細節](#技術實現細節)
- [向後兼容說明](#向後兼容說明)
- [使用範例](#使用範例)
- [故障排除](#故障排除)
- [性能考量](#性能考量)

## 🎯 概覽

TongRAG3 的重疊機制是一個智能文檔分塊重疊檢測和顯示系統，旨在：

1. **準確檢測文檔塊之間的實際重疊**
2. **清楚分離重疊內容和新內容**
3. **提供兩級顯示（簡潔列表+詳細查看）**
4. **向後兼容舊版本文檔**

### 核心優勢

```mermaid
graph LR
    A[文檔分塊] --> B[重疊檢測]
    B --> C[內容分離]
    C --> D[智能顯示]
    
    B --> E[數學計算]
    C --> F[文本格式化]
    D --> G[用戶體驗]
    
    E --> H[位置分析]
    F --> I[內容標記]
    G --> J[分級展示]
```

## 🧮 重疊機制數學原理

### 重疊檢測算法

重疊檢測基於文檔中的實際字符位置進行計算：

```python
def detect_overlap(chunk_i, chunk_i_minus_1):
    """
    重疊檢測的核心邏輯
    
    數學原理：
    - 如果 chunk_i.start_pos < chunk_i_minus_1.end_pos
    - 則存在重疊，重疊大小為 chunk_i_minus_1.end_pos - chunk_i.start_pos
    """
    has_overlap = chunk_i.start_pos < chunk_i_minus_1.end_pos
    
    if has_overlap:
        overlap_size = chunk_i_minus_1.end_pos - chunk_i.start_pos
        overlap_percentage = (overlap_size / len(chunk_i.text)) * 100
        
        # 文本分離
        overlap_text = original_text[chunk_i.start_pos : chunk_i_minus_1.end_pos]
        new_content = original_text[chunk_i_minus_1.end_pos : chunk_i.end_pos]
        
    return has_overlap, overlap_size, overlap_text, new_content
```

### 位置計算公式

```
文檔塊位置關係：
┌─────────────────────────────────────────┐
│  Chunk 1: [0────────────100]           │
│  Chunk 2:     [80──────────180]        │
│  Chunk 3:         [160─────────260]    │
└─────────────────────────────────────────┘

重疊檢測：
- Chunk 1: 無重疊（第一塊）
- Chunk 2: start_pos(80) < prev_end_pos(100) → 有重疊20字符
- Chunk 3: start_pos(160) < prev_end_pos(180) → 有重疊20字符
```

### 百分比計算

```
重疊百分比 = (重疊大小 / 當前塊總長度) × 100

範例：
- 當前塊總長度：120字符
- 重疊大小：20字符
- 重疊百分比：(20 / 120) × 100 = 16.7%
```

## 📊 元數據結構說明

### 核心元數據字段

每個文檔塊包含以下重疊相關元數據：

```json
{
  "chunk_id": 2,
  "text": "完整的塊內容...",
  "start_char": 80,
  "end_char": 180,
  "has_overlap": true,
  "overlap_size": 20,
  "overlap_text": "重疊的20個字符內容",
  "new_content": "除重疊外的新內容",
  "overlap_percentage": 16.7,
  "created_time": "2025-09-01T10:30:00",
  "file_size": 15420
}
```

### 字段詳細說明

| 字段名 | 類型 | 說明 | 範例 |
|--------|------|------|------|
| `chunk_id` | int | 塊編號（從1開始） | `2` |
| `start_char` | int | 在原文檔中的起始位置 | `80` |
| `end_char` | int | 在原文檔中的結束位置 | `180` |
| `has_overlap` | bool | 是否與前一塊有重疊 | `true` |
| `overlap_size` | int | 重疊字符數量 | `20` |
| `overlap_text` | str | 重疊部分的實際文本 | `"...重疊內容..."` |
| `new_content` | str | 去除重疊後的新內容 | `"...新內容..."` |
| `overlap_percentage` | float | 重疊百分比 | `16.7` |

### 特殊情況處理

```python
# 第一個塊：永遠無重疊
if chunk_id == 1:
    has_overlap = False
    overlap_size = 0
    overlap_text = ""
    new_content = full_text

# 無實際重疊的塊
if start_pos >= prev_end_pos:
    has_overlap = False
    # 其他字段設為默認值

# 舊版本兼容
if not has_new_metadata_fields:
    # 使用原始文本作為後備
    new_content = full_text
    overlap_text = ""
```

## 🎨 顯示格式規範

### 兩級顯示系統

TongRAG3 採用兩級顯示系統，提供從概覽到詳細的漸進式信息展示：

```
Level 1: 搜索列表（簡潔視圖）
├── 有重疊塊: 🟨重疊部分(完整) + 🟩新內容(截斷+剩餘字數)
└── 無重疊塊: 🟩獨有內容(截斷+剩餘字數)

Level 2: 詳細查看（完整視圖）
├── 有重疊塊: 📊重疊分析 + 🟨重疊部分 + 🟩新內容 + 📄完整內容
└── 無重疊塊: 🟩獨有內容(完整顯示所有文字)
```

### Level 1: 簡潔列表顯示

```python
def format_brief_display(result):
    """
    簡潔列表的格式化邏輯
    """
    if result['has_overlap']:
        # 有重疊的塊
        overlap_text = result['overlap_text']
        new_content = result['new_content']
        preview, remaining = truncate_text_with_count(new_content, 150)
        
        display = f"""
🟨 重疊部分: {overlap_text}
🟩 新內容: {preview}{'（還有 ' + str(remaining) + ' 個字）' if remaining > 0 else ''}
        """
    else:
        # 無重疊的塊
        full_content = result['text']
        preview, remaining = truncate_text_with_count(full_content, 150)
        
        display = f"""
🟩 獨有內容: {preview}{'（還有 ' + str(remaining) + ' 個字）' if remaining > 0 else ''}
        """
    
    return display
```

### Level 2: 詳細完整顯示

```python
def format_detailed_display(result):
    """
    詳細查看的格式化邏輯
    """
    if result['has_overlap']:
        # 有重疊的塊 - 提供完整的分析信息
        return f"""
📊 重疊分析:
   • 重疊大小: {result['overlap_size']} 字符
   • 重疊百分比: {result['overlap_percentage']:.1f}%
   • 塊編號: {result['chunk_id']}

🟨 重疊部分:
{result['overlap_text']}

🟩 新內容:
{result['new_content']}

📄 完整內容:
{result['text']}
        """
    else:
        # 無重疊的塊 - 直接顯示完整內容
        return f"""
🟩 獨有內容:
{result['text']}
        """
```

### 顏色和圖標系統

| 元素 | 圖標 | 顏色 | 含義 |
|------|------|------|------|
| 重疊部分 | 🟨 | 黃色背景 | 與前一塊重複的內容 |
| 新內容 | 🟩 | 綠色背景 | 該塊獨有的新內容 |
| 分析信息 | 📊 | 藍色字體 | 重疊統計和分析 |
| 完整內容 | 📄 | 白色字體 | 塊的完整文本 |

## 🔧 技術實現細節

### 1. document_loader.py 重疊檢測實現

```python
def split_text(self, text: str, file_path: str) -> List[Dict]:
    """
    智能文本分塊與重疊檢測
    
    關鍵改進：
    1. 先計算所有塊的位置
    2. 再遍歷計算重疊關係
    3. 基於實際位置而非配置參數
    """
    # 計算所有位置
    positions = self._calculate_chunk_positions(text)
    
    chunks = []
    for i, (start_pos, end_pos) in enumerate(positions):
        chunk_text = text[start_pos:end_pos].strip()
        
        # 重疊檢測邏輯
        has_overlap = False
        overlap_size = 0
        overlap_text = ""
        new_content = chunk_text
        overlap_percentage = 0.0
        
        if i > 0:  # 不是第一個塊
            prev_end_pos = positions[i-1][1]
            if start_pos < prev_end_pos:  # 實際重疊
                has_overlap = True
                overlap_size = prev_end_pos - start_pos
                overlap_text = text[start_pos:prev_end_pos]
                new_content = text[prev_end_pos:end_pos].strip()
                overlap_percentage = (overlap_size / len(chunk_text)) * 100
        
        # 構建塊元數據
        chunks.append({
            # ... 其他字段
            "has_overlap": has_overlap,
            "overlap_size": overlap_size,
            "overlap_text": overlap_text,
            "new_content": new_content,
            "overlap_percentage": overlap_percentage
        })
    
    return chunks
```

### 2. utils/search.py 結果格式化增強

```python
def format_results(results: List[Dict]) -> List[Dict]:
    """
    增強的結果格式化，支持準確的重疊信息處理
    
    關鍵特性：
    1. 從元數據提取重疊信息
    2. 向後兼容舊版本數據
    3. 智能文本分離和清理
    """
    for i, result in enumerate(results):
        metadata = result.get("metadata", {})
        
        # 提取重疊信息
        has_overlap = metadata.get("has_overlap", False)
        overlap_text = metadata.get("overlap_text", "")
        new_content = metadata.get("new_content", "")
        
        # 向後兼容處理
        if not overlap_text and not new_content and has_overlap:
            # 對舊數據進行兼容性處理
            clean_content = clean_text(result.get("text", ""))
            overlap_size = metadata.get("overlap_size", 0)
            
            if overlap_size > 0:
                overlap_text = clean_content[:overlap_size]
                new_content = clean_content[overlap_size:]
            else:
                new_content = clean_content
        
        # 格式化輸出
        formatted_result = {
            # ... 標準字段
            "has_overlap": has_overlap,
            "overlap_text": clean_text(overlap_text),
            "new_content": clean_text(new_content),
            "overlap_percentage": metadata.get("overlap_percentage", 0.0)
        }
        
        formatted_results.append(formatted_result)
    
    return formatted_results
```

### 3. ui/ui_search/display.py 顯示邏輯

```python
def display_single_result(self, result: Dict) -> None:
    """
    單個搜索結果的智能顯示
    
    顯示邏輯：
    1. 檢查是否有重疊
    2. 根據重疊狀態選擇顯示格式
    3. 應用顏色和圖標標記
    """
    rank = result.get("rank", "?")
    source = result.get("source", "未知來源")
    score = result.get("score_display", "N/A")
    
    # 顯示基本信息
    print(f"\n[{rank}] {source} (相關度: {score})")
    
    # 智能內容顯示
    if result.get("has_overlap", False):
        # 有重疊的塊 - 分別顯示重疊和新內容
        overlap_text = result.get("overlap_text", "")
        new_content = result.get("new_content", "")
        overlap_pct = result.get("overlap_percentage", 0.0)
        
        if overlap_text:
            self.print_colored(f"🟨 重疊部分 ({overlap_pct:.1f}%): {overlap_text}", "yellow")
        
        if new_content:
            preview, remaining = truncate_text_with_count(new_content, 150)
            remaining_info = f"（還有 {remaining} 個字）" if remaining > 0 else ""
            self.print_colored(f"🟩 新內容: {preview}{remaining_info}", "green")
    else:
        # 無重疊的塊 - 直接顯示內容
        full_content = result.get("text", "")
        preview, remaining = truncate_text_with_count(full_content, 150)
        remaining_info = f"（還有 {remaining} 個字）" if remaining > 0 else ""
        self.print_colored(f"🟩 獨有內容: {preview}{remaining_info}", "green")

def show_full_content(self, result: Dict) -> None:
    """
    顯示完整內容的詳細信息
    """
    if result.get("has_overlap", False):
        # 有重疊 - 提供完整分析
        self._show_overlap_analysis(result)
        self._show_overlap_content(result)
        self._show_new_content(result)
        self._show_full_content(result)
    else:
        # 無重疊 - 直接顯示完整內容
        self._show_complete_content(result)
```

## 🔄 向後兼容說明

### 兼容性策略

TongRAG3 的重疊機制設計了完整的向後兼容策略：

```python
def ensure_backward_compatibility(metadata):
    """
    向後兼容性處理邏輯
    
    處理情況：
    1. 舊版本文檔沒有重疊元數據
    2. 部分更新的文檔缺少某些字段
    3. 格式不一致的歷史數據
    """
    # 檢查是否有新的重疊字段
    has_new_fields = all(key in metadata for key in [
        'has_overlap', 'overlap_text', 'new_content'
    ])
    
    if not has_new_fields:
        # 舊版本兼容：顯示為無重疊
        return {
            'has_overlap': False,
            'overlap_size': 0,
            'overlap_text': '',
            'new_content': metadata.get('text', ''),
            'overlap_percentage': 0.0
        }
    
    return metadata  # 使用新版本數據
```

### 兼容性測試矩陣

| 文檔版本 | 元數據完整性 | 顯示行為 | 兼容狀態 |
|----------|------------|----------|----------|
| 新版本 | 完整重疊信息 | 智能兩級顯示 | ✅ 完全支持 |
| 舊版本 | 無重疊信息 | 顯示為無重疊 | ✅ 正常兼容 |
| 混合版本 | 部分重疊信息 | 智能填充缺失 | ✅ 自動修復 |
| 損壞數據 | 格式錯誤 | 降級到基本顯示 | ✅ 優雅處理 |

### 遷移建議

```bash
# 對於想要充分利用新重疊功能的用戶
# 建議重新處理文檔以獲得完整的重疊信息

# 1. 備份現有數據
cp -r chroma_data chroma_data_backup

# 2. 重新導入文檔（可選）
python main.py
# 選擇選項 10: 重建向量資料庫

# 3. 驗證重疊功能
python main.py
# 執行搜索，檢查重疊顯示效果
```

## 📖 使用範例

### 範例 1：標準重疊顯示

```
執行搜索：python main.py → 選項1（語義搜索）→ 查詢"人工智能技術"

結果顯示：
┌─────────────────────────────────────────────┐
│ [1] AI技術發展.md (相關度: 85.3%)            │
│ 🟨 重疊部分 (15.2%): 人工智能技術在現代社會中... │
│ 🟩 新內容: 發揮著越來越重要的作用，特別是在...   │
│    （還有 230 個字）                         │
├─────────────────────────────────────────────┤
│ [2] 機器學習基礎.md (相關度: 78.9%)          │
│ 🟩 獨有內容: 機器學習是人工智能的核心分支...    │
│    （還有 189 個字）                         │
└─────────────────────────────────────────────┘

輸入編號查看詳情：
輸入 1 → 查看第一個結果的完整重疊分析
輸入 2 → 查看第二個結果的完整內容
```

### 範例 2：詳細重疊分析

```
輸入 1 查看完整內容：

┌──────────── 詳細內容分析 ────────────┐
│                                      │
│ 📊 重疊分析:                          │
│    • 重疊大小: 45 字符                │
│    • 重疊百分比: 15.2%               │
│    • 塊編號: 2                       │
│                                      │
│ 🟨 重疊部分:                          │
│ 人工智能技術在現代社會中扮演著重要角色， │
│ 從自動駕駛到智能語音助手，AI的應用...    │
│                                      │
│ 🟩 新內容:                           │
│ 發揮著越來越重要的作用，特別是在醫療、  │
│ 金融和教育領域。隨著深度學習和神經網路  │
│ 技術的不斷進步，AI系統的性能和準確度... │
│                                      │
│ 📄 完整內容:                         │
│ [顯示該文檔塊的完整文本內容]            │
│                                      │
└──────────────────────────────────────┘

按 Enter 返回搜索結果列表...
```

### 範例 3：無重疊內容顯示

```
輸入 2 查看完整內容：

┌──────────── 完整內容 ───────────────┐
│                                     │
│ 🟩 獨有內容:                         │
│                                     │
│ 機器學習是人工智能的核心分支，通過讓 │
│ 計算機從數據中學習模式和規律，實現智 │
│ 能決策。主要包括監督學習、無監督學習 │
│ 和強化學習三大類別。監督學習使用標註 │
│ 數據訓練模型，如分類和回歸問題；無監 │
│ 督學習處理無標註數據，發現隱藏結構； │
│ 強化學習通過獎勵機制訓練智能體在環境 │
│ 中做出最優決策...                    │
│                                     │
└─────────────────────────────────────┘

按 Enter 返回搜索結果列表...
```

## 🔧 故障排除

### 常見問題和解決方案

#### 問題 1：重疊信息顯示不正確

**症狀**：
- 明顯有重疊的塊顯示為無重疊
- 重疊百分比計算錯誤
- 重疊文本截斷不當

**診斷步驟**：
```python
# 檢查元數據完整性
def diagnose_overlap_metadata(result):
    metadata = result.get('metadata', {})
    required_fields = ['has_overlap', 'overlap_size', 'overlap_text', 'new_content']
    
    print("元數據診斷：")
    for field in required_fields:
        value = metadata.get(field, "缺失")
        print(f"  {field}: {value}")
    
    # 檢查數據一致性
    if metadata.get('has_overlap') and not metadata.get('overlap_text'):
        print("⚠️  警告：標記有重疊但重疊文本為空")
    
    if metadata.get('overlap_size', 0) != len(metadata.get('overlap_text', '')):
        print("⚠️  警告：重疊大小與重疊文本長度不匹配")
```

**解決方案**：
1. 重新處理相關文檔
2. 檢查 CHUNK_OVERLAP 配置
3. 驗證文檔載入邏輯

#### 問題 2：顯示格式混亂

**症狀**：
- 顏色標記顯示異常
- 圖標無法正確顯示
- 文本格式錯亂

**解決方案**：
```bash
# 檢查終端支援
echo $TERM
# 安裝必要的字體和終端工具
pip install colorama

# 測試顯示功能
python -c "
from colorama import init, Fore, Back
init()
print(f'{Back.YELLOW}🟨 測試黃色背景{Back.RESET}')
print(f'{Back.GREEN}🟩 測試綠色背景{Back.RESET}')
"
```

#### 問題 3：性能問題

**症狀**：
- 重疊檢測耗時過長
- 大文檔處理緩慢
- 內存使用過高

**優化建議**：
```python
# 調整分塊參數
CHUNK_SIZE = 800        # 增大塊大小
CHUNK_OVERLAP = 100     # 減小重疊大小

# 啟用快取機制
ENABLE_RESULT_CACHE = True
CACHE_SIZE = 500        # 快取結果數量

# 並行處理大文檔
MAX_WORKERS = 4         # 並行工作線程數
```

### 調試工具

```python
def debug_overlap_detection():
    """
    重疊檢測調試工具
    """
    # 載入測試文檔
    loader = DocumentLoader()
    chunks = loader.split_text(test_content, "test.txt")
    
    print("=== 重疊檢測調試報告 ===")
    for i, chunk in enumerate(chunks):
        print(f"\n塊 {i+1}:")
        print(f"  位置: {chunk['start_char']}-{chunk['end_char']}")
        print(f"  有重疊: {chunk['has_overlap']}")
        if chunk['has_overlap']:
            print(f"  重疊大小: {chunk['overlap_size']}")
            print(f"  重疊百分比: {chunk['overlap_percentage']:.1f}%")
            print(f"  重疊文本: '{chunk['overlap_text'][:50]}...'")
            print(f"  新內容: '{chunk['new_content'][:50]}...'")
```

## ⚡ 性能考量

### 性能優化策略

#### 1. 重疊檢測優化

```python
class OptimizedDocumentLoader:
    def __init__(self):
        self.position_cache = {}  # 位置快取
        self.overlap_cache = {}   # 重疊計算快取
    
    def _calculate_chunk_positions_cached(self, text: str):
        """快取位置計算結果"""
        text_hash = hash(text)
        if text_hash in self.position_cache:
            return self.position_cache[text_hash]
        
        positions = self._calculate_chunk_positions(text)
        self.position_cache[text_hash] = positions
        return positions
```

#### 2. 顯示優化

```python
def lazy_content_loading():
    """
    懶加載內容顯示
    
    只在用戶請求詳細查看時才進行複雜的格式化
    """
    # Level 1: 僅載入必要的預覽信息
    preview_data = {
        'rank': result['rank'],
        'source': result['source'], 
        'has_overlap': result['has_overlap'],
        'preview_text': result['preview']
    }
    
    # Level 2: 用戶請求時才載入完整內容
    if user_requests_details:
        detailed_data = load_full_overlap_analysis(result)
        return merge_data(preview_data, detailed_data)
    
    return preview_data
```

#### 3. 內存管理

```python
def memory_efficient_processing():
    """
    內存高效的重疊處理
    """
    # 批次處理大文檔
    def process_in_batches(content, batch_size=1000):
        for i in range(0, len(content), batch_size):
            batch = content[i:i + batch_size]
            yield process_batch(batch)
    
    # 及時清理臨時對象
    def cleanup_temp_objects():
        import gc
        gc.collect()  # 強制垃圾回收
```

### 性能基準

| 操作 | 小文檔 (<1KB) | 中文檔 (1-10KB) | 大文檔 (>10KB) |
|------|---------------|----------------|----------------|
| 重疊檢測 | <1ms | <5ms | <20ms |
| 格式化顯示 | <1ms | <3ms | <10ms |
| 完整內容載入 | <1ms | <2ms | <8ms |
| 內存使用 | <1MB | <5MB | <20MB |

### 優化建議

```yaml
性能優化配置:
  # 基礎配置
  CHUNK_SIZE: 1000          # 較大的塊減少總塊數
  CHUNK_OVERLAP: 50         # 適中的重疊平衡效果和性能
  
  # 快取配置
  ENABLE_POSITION_CACHE: true
  ENABLE_RESULT_CACHE: true
  CACHE_SIZE: 1000
  
  # 並行配置
  MAX_WORKERS: 4            # 根據CPU核心數調整
  BATCH_SIZE: 100          # 批次處理大小
  
  # 顯示優化
  LAZY_LOADING: true        # 啟用懶加載
  PREVIEW_LENGTH: 150       # 預覽文本長度
```

---

## 📞 技術支持

### 相關文檔
- [ARCHITECTURE.md](ARCHITECTURE.md) - 系統整體架構
- [CONFIGURATION.md](CONFIGURATION.md) - 配置選項說明
- [TROUBLESHOOTING.md](TROUBLESHOOTING.md) - 故障排除指南

### 問題反饋
如果遇到重疊機制相關問題，請提供：
1. 具體的搜索查詢和結果
2. 相關文檔的元數據信息
3. 錯誤日誌和堆疊追蹤
4. 環境配置信息

**文檔版本**: v1.0  
**最後更新**: 2025-09-01  
**適用系統**: TongRAG3 v0.2.1+

---

*TongRAG3 重疊機制 - 讓文檔搜索更精確、更智能*