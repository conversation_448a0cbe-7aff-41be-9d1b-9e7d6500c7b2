"""
Search Engine Module (Backward Compatibility Layer)

This module maintains 100% backward compatibility with the original search_engine.py
while leveraging the new modular architecture. All existing imports and usage
patterns continue to work unchanged.

The actual implementation has been refactored into a modular structure under
the src/search/ package, with specialized modules for different search types
and optional reranking support.

New Features Available:
- Modular architecture for better maintainability  
- Optional reranking support to improve search quality
- Enhanced search methods with additional parameters
- Better error handling and logging

For new projects, consider importing directly from src.search for access to 
the full modular API and enhanced features.
"""

# Import everything from the new modular search package
# This ensures 100% backward compatibility
from .search import *
from .search.engine import ChromaSearchEngine

# Re-export the main class to maintain exact same import behavior
__all__ = ['ChromaSearchEngine']

# Version and metadata for compatibility tracking
__version__ = "2.0.0"  # Modular version
__compatibility_version__ = "1.0.0"  # Original version compatibility
__migration_status__ = "complete"

# Optional: Add deprecation notice for development tracking
import logging
logger = logging.getLogger(__name__)

# Log the migration (only in debug mode to avoid spam)
logger.debug(
    "search_engine.py: Using modular architecture from src.search package. "
    "All functionality preserved with additional reranking support available."
)

def get_new_features():
    """
    Get information about new features available in the modular version.
    
    Returns:
        Dictionary describing new features and capabilities
    """
    return {
        "reranker_support": "Optional reranking to improve search result quality",
        "enhanced_search_methods": "Multi-keyword, wildcard, hybrid, and batch search",
        "modular_architecture": "Better maintainability and testing",
        "backward_compatibility": "100% compatible - no migration required"
    }