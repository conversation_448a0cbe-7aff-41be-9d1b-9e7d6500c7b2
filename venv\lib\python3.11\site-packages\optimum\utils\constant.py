# Copyright 2023 The HuggingFace Inc. team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.


CONFIG_NAME = "config.json"
ONNX_WEIGHTS_NAME = "model.onnx"

DIFFUSION_MODEL_UNET_SUBFOLDER = "unet"
DIFFUSION_MODEL_TRANSFORMER_SUBFOLDER = "transformer"
DIFFUSION_MODEL_VAE_DECODER_SUBFOLDER = "vae_decoder"
DIFFUSION_MODEL_VAE_ENCODER_SUBFOLDER = "vae_encoder"
DIFFUSION_MODEL_TEXT_ENCODER_SUBFOLDER = "text_encoder"
DIFFUSION_MODEL_TEXT_ENCODER_2_SUBFOLDER = "text_encoder_2"
DIFFUSION_MODEL_TEXT_ENCODER_3_SUBFOLDER = "text_encoder_3"
DIFFUSION_PIPELINE_CONFIG_FILE_NAME = "model_index.json"
DIFFUSION_MODEL_CONFIG_FILE_NAME = "config.json"
DIFFUSION_MODEL_ONNX_FILE_NAME = "model.onnx"
