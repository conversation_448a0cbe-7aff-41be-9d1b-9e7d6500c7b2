{"timestamp": "2025-08-30T07:26:22.782912", "query": "GMT 排線規定", "comparison_type": "search_methods", "results": {"semantic": {"name": "語義搜索", "results": [{"id": "chunk_4", "text": ".xlsx \n9.M8系模組 , 1.未來新案皆使用LOWPIN Board.2.Tony有 UR commom code3.20250303有宣導mail主旨: RE: 宣導-模組資訊參考 \n11. WSS TE 目錄下常用路徑 , 1.經驗分享路徑: wssTESTTE_工作記錄經驗分享集 2.測試設備,測試機說明書 : wssTESTTE_工作記錄經驗分享集機台相關3.新人教育訓練,部門,量產相關規定 : wssTEST測試相關規定 \n12. Fab MAP路徑 , wssphoto_die_map$ \n13. GMT各機台排線規格如下: (包含 SMA、SMB長度) , ASL-1000 可容許:30CM,60CM (因產品特性和Handler不同而有所差異)ASLX & CTA8290: 60CMCTA8280F:150CM \n14. 量產測試時間與 Release form 不一致之處理協議ETD/逸昌測時問題 , **主旨：**FW: (測時問題) G2732PK81D-K(AD) product test time are different (2023.07.", "metadata": {"chunk_id": 4, "end_char": 1729, "file_name": "部門rule宣導_20250509.md", "has_overlap": true, "created_time": "2025-08-29T22:24:51.826609", "file_size": 7437, "start_char": 1233, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md"}, "distance": 0.48035383224487305, "source": ""}, {"id": "chunk_11", "text": "ex : G7328Q51U(AD-TEMP01)zz : 針對有不同流程需同時並存的序號碼。b. 請生管建立對應的產品型號後 release。5. For EzFlow TEMP 程式命名規定a. GMT_G1234_T1_F1_01TEMPxx (xx : 為版本序) \n委外測試要點檔案名稱命名規則 , 檔案名稱 : 產品型號_測試機代碼&sites數量_站別_外測廠_TEXXXXXXXXX_版次.pdf(站別=FT or CP ,XXXXXXXXX=9碼數字)Ex :G6026_CTAF2_FT_JSSI_TE250326028.pdf產品型號 : 非銷售料號，可以單一型號or群組代碼表示 => G1234測試機代碼 : 依照程式名稱的測試機代碼及Site數表示 => TX1站別 : 區分 FT、CP、CSP外測廠 : 以英文表示，詳見列表特殊用途 : 正式版不用填寫，ENG以及TEMP須填寫版次 : 從1.0版次開始，小數點是否使用依個人分類習慣.", "metadata": {"chunk_id": 11, "has_overlap": true, "start_char": 4011, "file_size": 7437, "created_time": "2025-08-29T22:24:51.826609", "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_name": "部門rule宣導_20250509.md", "end_char": 4447}, "distance": 0.5161450505256653, "source": ""}, {"id": "chunk_10", "text": "需要上系統正式 release且使用專用型號。(非必要性測項須刪減或減少測試流程以減少測試成本)。如為特殊緣故須使用完整版測試程式重新 Re-screen 良品則採用正式版程式，不適用TEMP。b. ENG 則為 try 程式用, 不需要發 release form, 需在1週或兩批(後到者算) release 正式版程式.2. 命名規則 : (ex. 目前量產程式為 GMT_G1234AB_T1_F1_02 )a. TEMP 程式名稱則為 : GMT_G1234AB_T1_F1_02TEMPxx.b. ENG 則為 GMT_G1234AB_T1_F1_03ENGxxxx : 01 ~ 99.3. 注意事項 :a. TEMP字串前的版本為目前程式版本.b. ENG 字串前的版本則為下一版程式版本.4. EzFlow TEMP 型號定義 : a. 型號格式為 GXXXX(版本-TEMPzz) ex : G7328Q51U(AD-TEMP01)zz : 針對有不同流程需同時並存的序號碼。b. 請生管建立對應的產品型號後 release。5. For EzFlow TEMP 程式命名規定a.", "metadata": {"chunk_id": 10, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_name": "部門rule宣導_20250509.md", "end_char": 4111, "file_size": 7437, "has_overlap": true, "start_char": 3610, "created_time": "2025-08-29T22:24:51.826609"}, "distance": 0.5319573283195496, "source": ""}, {"id": "chunk_6", "text": "常由逸昌自負。 \n15. GTK/超豐 CP RELEASE FORM , 超豐cp 發一份即可 但是 RELEASE 時候寫2份 抽測與全測產品名稱要不一樣 要寫NOTE 可以問LPC,Allan.K \n17. MOS CP相關資訊 , 1. 最新UIS testnote 資訊 MDS1527 192.168.1.60tekevin.wangMDS1527DOCMDS1527 CP test note_20250314_Vicky最終確認.xlsx2. 最新CP test note 在sheet MOS CP test note 192.168.1.60tekevin.wangGMOSGMOSGM4466_無waferGM4466 CP testnote_20250312.xlsx3. AK update item1,2 data在 D:toolsAI TEST10.AI_MOS4. MOS MDS1527已經測過CP1 所以分bin與GMT定義不同,全新MOS測試 要按照 new test note(made by Ricky),PAT =215.", "metadata": {"end_char": 2514, "has_overlap": true, "file_size": 7437, "chunk_id": 6, "created_time": "2025-08-29T22:24:51.826609", "start_char": 2031, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_name": "部門rule宣導_20250509.md"}, "distance": 0.5395433902740479, "source": ""}, {"id": "chunk_7", "text": "TEST10.AI_MOS4. MOS MDS1527已經測過CP1 所以分bin與GMT定義不同,全新MOS測試 要按照 new test note(made by <PERSON>),PAT =215.MOS產品一律跑PAT,是自動流程 PAT item與BIN與MSEC談好 PAT item VBDSS,VGS .PAT FAL BIN :21 \n18. MOS 建立流程/新人MOS學習參考 , 1.Wsstest文件原始檔MSEC_MOS_委外相關資訊V1.0.xlsx =\n19. Fab MAP相關規範 , 1.於 2025/4/9 (週三) 下午 04:48 MALI主旨:FW: G3730AE 測試資料 MAIL內容: 理論上我們是不需再提供map 給外測廠. '\n20. MDS1527 流程 , 1. 原廠map(缺口下) 轉90度 為了 prober手臂不轉(轉會破片) (CP1) 2. CP1 測試UIS全部die (CP1 fail bin 33~64 原廠 fail bin 1~32)3. marge map CP1 + 原廠map marge rule (2025/4/18 mail to Kevin.", "metadata": {"created_time": "2025-08-29T22:24:51.826609", "end_char": 2933, "file_size": 7437, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "start_char": 2414, "file_name": "部門rule宣導_20250509.md", "has_overlap": true, "chunk_id": 7}, "distance": 0.5429896712303162, "source": ""}, {"id": "chunk_8", "text": "e (CP1 fail bin 33~64 原廠 fail bin 1~32)3. marge map CP1 + 原廠map marge rule (2025/4/18 mail to <PERSON><PERSON><PERSON>)4. MDS1527合併流程: C:UsersAkiraLinOneDriveMDS1527合併Merge 分Bin_20250417_最終合併流程.xlsx \n21. 華天西安 hold lot網頁反饋作業 , 華天西安廠推出hold lot 於網頁上直接處理。免除mail來往及放長假忘記回覆，或華天對應人員離職而未處理。操作指導書如附件。雙週會會為各位解說示範操作。C:UsersAkiraLinOneDrive文件6.AI訓練資料基本RULE判斷1.0文件原始檔Hold Lot网?化反?作?指?-客?.pptx \n測試機=測試機代碼 \nASL-X=AX \nASL-1K V4.82(NT)=T \nASL-1K V6.3(XP=TX \nT861=H \nCTA-8280H=CTA \nCTA-8280F=CTAF \nCTA-8290D=CTAX \nCTA-8290DPlus=CTAX", "metadata": {"has_overlap": true, "chunk_id": 8, "created_time": "2025-08-29T22:24:51.826609", "end_char": 3333, "start_char": 2833, "file_size": 7437, "file_name": "部門rule宣導_20250509.md", "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md"}, "distance": 0.5697169303894043, "source": ""}, {"id": "chunk_2", "text": ":4塊3.Friday, October 18, 2024 9:20 AM 由Xu Yongyu 徐永雨mail通知,mail主旨:G948程式配件寄送 \n6.MSEC_CP_小die的限制 , 1.一般 Prober 限制 :Die size (含 scribe line) 必須 >500×500 μm，僅適用於 6吋產品。2.MSEC Prober 限制:UF200A (8吋) >320×320 μm，UF200 (8吋) 500×800 μm。3.GMT Prober 限制:受機台型號及軟體版本影響，UF200 無法測試 >300×300 μm。4.小 Die Alignment 問題:<500×500 μm 時，Alignment 經常失敗，需手動調整，存在風險。5.ESI9350 系列 (Laser 加工能力):最大生產數量：45000 ea；Fuse 根數限制：20000-30000 根。6.ESI9830 系列 (Laser 加工能力):最大生產數量：100000 ea；Fuse 根數尚未遇到問題。7.Fuse Process 限制:單次 process Fuse 根數須低於 20000-30000 根，超過可能導致問題。", "metadata": {"end_char": 926, "chunk_id": 2, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_size": 7437, "has_overlap": true, "file_name": "部門rule宣導_20250509.md", "created_time": "2025-08-29T22:24:51.826609", "start_char": 400}, "distance": 0.5698552131652832, "source": ""}, {"id": "chunk_1", "text": "1.CTA & CTAF 價格資訊 , 1.在大陸地區，CTA 與 CTAF 每小時價格相同，資訊來源 PC-IVY。詳情需洽 PC-BOSS，但無法進一步提供。 \n2.IC 壓塊規範 , 1.需經 TE 驗證，確保測試環境相符。2.尺寸：需符合 IC 大小。3.壓頭：空心設計，金屬材質。 \n3.WLCSP Frame Prober 測試使用時機 , 1.針對切割後產生 crack 並已有客訴的特定 WLCSP 產品，才開啟 Frame Prober 測試。一般情況一律不使用，避免額外成本與流程。 \n4.Release Form 與 DLL 時間規則 , 1.DLL 時間應早於或不超過 Release Form 1 天；否則視為異常，需重新 Release。確保程式發行與 DLL 版本完全匹配，避免未正式 Release 的修改。 \n5.滁州) 標準配置 , 1.FOVI:4塊2.FPVI:4塊3.Friday, October 18, 2024 9:20 AM 由Xu Yongyu 徐永雨mail通知,mail主旨:G948程式配件寄送 \n6.MSEC_CP_小die的限制 , 1.", "metadata": {"chunk_id": 1, "end_char": 500, "has_overlap": false, "file_name": "部門rule宣導_20250509.md", "start_char": 0, "created_time": "2025-08-29T22:24:51.826609", "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_size": 7437}, "distance": 0.5717675685882568, "source": ""}, {"id": "chunk_3", "text": "加工能力):最大生產數量：100000 ea；Fuse 根數尚未遇到問題。7.Fuse Process 限制:單次 process Fuse 根數須低於 20000-30000 根，超過可能導致問題。 \n7.CST Socket Repeat Order 流程 , 步驟1: PC產品負責人提出mail確認handler type (P&P or 轉塔機).步驟2:TE產品負責人Confirm handler type後回信.步驟3:PC(Andy)負責提出請購需求給CST.步驟4:CST回復報價與交期. , \n8.模組化清單,模組查詢,關於跨組模組 , 1.如果工程師有新產品跟其他組類似,例如我們有接到power switch,就需要與他組相關人員討論, 使用哪一套配件開發.2.目前MOTOR系列使用LOWPIN board 規劃3.模組化清單路徑 wssTEST測試產品模組暨測試開發參考測試模組化清單.xlsx \n9.M8系模組 , 1.未來新案皆使用LOWPIN Board.2.Tony有 UR commom code3.20250303有宣導mail主旨: RE: 宣導-模組資訊參考 \n11.", "metadata": {"file_name": "部門rule宣導_20250509.md", "start_char": 826, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_size": 7437, "has_overlap": true, "chunk_id": 3, "end_char": 1333, "created_time": "2025-08-29T22:24:51.826609"}, "distance": 0.579529881477356, "source": ""}, {"id": "chunk_9", "text": "(NT)=T \nASL-1K V6.3(XP=TX \nT861=H \nCTA-8280H=CTA \nCTA-8280F=CTAF \nCTA-8290D=CTAX \nCTA-8290DPlus=CTAXP \nAST-2000=AST \nSTS-8200=STS \nSTS-8202=STS \nETS-series=E \nS100=YS \n測試廠=測試廠代碼 \n逸昌=ETD \n超豐=GTK \n微矽=MSEC \n菱生=LS\n南岩=NANO\n久元=YTEC\n長電先進 , JCAP \n長電 , JCET \n通富微 , TFME \n天水華天 , TSHT \n西安華天 , XAHT \n江蘇芯德 , JSSI \n測試程式&治具命名規則 \nTEMP & ENG 程式使用規定 , 1. 適用範圍 :a. TEMP專為 Re-screen良品所使用(重測完才可出貨), 需要上系統正式 release且使用專用型號。(非必要性測項須刪減或減少測試流程以減少測試成本)。如為特殊緣故須使用完整版測試程式重新 Re-screen 良品則採用正式版程式，不適用TEMP。b.", "metadata": {"has_overlap": true, "end_char": 3710, "file_name": "部門rule宣導_20250509.md", "file_size": 7437, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "created_time": "2025-08-29T22:24:51.826609", "chunk_id": 9, "start_char": 3233}, "distance": 0.586730420589447, "source": ""}], "count": 10}, "keyword": {"name": "關鍵詞搜索", "results": [], "count": 0}, "hybrid": {"name": "混合搜索", "results": [{"id": "chunk_4", "text": ".xlsx \n9.M8系模組 , 1.未來新案皆使用LOWPIN Board.2.Tony有 UR commom code3.20250303有宣導mail主旨: RE: 宣導-模組資訊參考 \n11. WSS TE 目錄下常用路徑 , 1.經驗分享路徑: wssTESTTE_工作記錄經驗分享集 2.測試設備,測試機說明書 : wssTESTTE_工作記錄經驗分享集機台相關3.新人教育訓練,部門,量產相關規定 : wssTEST測試相關規定 \n12. Fab MAP路徑 , wssphoto_die_map$ \n13. GMT各機台排線規格如下: (包含 SMA、SMB長度) , ASL-1000 可容許:30CM,60CM (因產品特性和Handler不同而有所差異)ASLX & CTA8290: 60CMCTA8280F:150CM \n14. 量產測試時間與 Release form 不一致之處理協議ETD/逸昌測時問題 , **主旨：**FW: (測時問題) G2732PK81D-K(AD) product test time are different (2023.07.", "metadata": {"end_char": 1729, "file_name": "部門rule宣導_20250509.md", "created_time": "2025-08-29T22:24:51.826609", "has_overlap": true, "file_size": 7437, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "chunk_id": 4, "start_char": 1233}, "distance": 0.48035383224487305, "source": ""}, {"id": "chunk_11", "text": "ex : G7328Q51U(AD-TEMP01)zz : 針對有不同流程需同時並存的序號碼。b. 請生管建立對應的產品型號後 release。5. For EzFlow TEMP 程式命名規定a. GMT_G1234_T1_F1_01TEMPxx (xx : 為版本序) \n委外測試要點檔案名稱命名規則 , 檔案名稱 : 產品型號_測試機代碼&sites數量_站別_外測廠_TEXXXXXXXXX_版次.pdf(站別=FT or CP ,XXXXXXXXX=9碼數字)Ex :G6026_CTAF2_FT_JSSI_TE250326028.pdf產品型號 : 非銷售料號，可以單一型號or群組代碼表示 => G1234測試機代碼 : 依照程式名稱的測試機代碼及Site數表示 => TX1站別 : 區分 FT、CP、CSP外測廠 : 以英文表示，詳見列表特殊用途 : 正式版不用填寫，ENG以及TEMP須填寫版次 : 從1.0版次開始，小數點是否使用依個人分類習慣.", "metadata": {"has_overlap": true, "file_size": 7437, "chunk_id": 11, "start_char": 4011, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "created_time": "2025-08-29T22:24:51.826609", "end_char": 4447, "file_name": "部門rule宣導_20250509.md"}, "distance": 0.5161450505256653, "source": ""}, {"id": "chunk_10", "text": "需要上系統正式 release且使用專用型號。(非必要性測項須刪減或減少測試流程以減少測試成本)。如為特殊緣故須使用完整版測試程式重新 Re-screen 良品則採用正式版程式，不適用TEMP。b. ENG 則為 try 程式用, 不需要發 release form, 需在1週或兩批(後到者算) release 正式版程式.2. 命名規則 : (ex. 目前量產程式為 GMT_G1234AB_T1_F1_02 )a. TEMP 程式名稱則為 : GMT_G1234AB_T1_F1_02TEMPxx.b. ENG 則為 GMT_G1234AB_T1_F1_03ENGxxxx : 01 ~ 99.3. 注意事項 :a. TEMP字串前的版本為目前程式版本.b. ENG 字串前的版本則為下一版程式版本.4. EzFlow TEMP 型號定義 : a. 型號格式為 GXXXX(版本-TEMPzz) ex : G7328Q51U(AD-TEMP01)zz : 針對有不同流程需同時並存的序號碼。b. 請生管建立對應的產品型號後 release。5. For EzFlow TEMP 程式命名規定a.", "metadata": {"start_char": 3610, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "end_char": 4111, "chunk_id": 10, "file_name": "部門rule宣導_20250509.md", "created_time": "2025-08-29T22:24:51.826609", "has_overlap": true, "file_size": 7437}, "distance": 0.5319573283195496, "source": ""}, {"id": "chunk_6", "text": "常由逸昌自負。 \n15. GTK/超豐 CP RELEASE FORM , 超豐cp 發一份即可 但是 RELEASE 時候寫2份 抽測與全測產品名稱要不一樣 要寫NOTE 可以問LPC,Allan.K \n17. MOS CP相關資訊 , 1. 最新UIS testnote 資訊 MDS1527 192.168.1.60tekevin.wangMDS1527DOCMDS1527 CP test note_20250314_Vicky最終確認.xlsx2. 最新CP test note 在sheet MOS CP test note 192.168.1.60tekevin.wangGMOSGMOSGM4466_無waferGM4466 CP testnote_20250312.xlsx3. AK update item1,2 data在 D:toolsAI TEST10.AI_MOS4. MOS MDS1527已經測過CP1 所以分bin與GMT定義不同,全新MOS測試 要按照 new test note(made by Ricky),PAT =215.", "metadata": {"created_time": "2025-08-29T22:24:51.826609", "start_char": 2031, "chunk_id": 6, "file_name": "部門rule宣導_20250509.md", "has_overlap": true, "end_char": 2514, "file_size": 7437, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md"}, "distance": 0.5395433902740479, "source": ""}, {"id": "chunk_7", "text": "TEST10.AI_MOS4. MOS MDS1527已經測過CP1 所以分bin與GMT定義不同,全新MOS測試 要按照 new test note(made by <PERSON>),PAT =215.MOS產品一律跑PAT,是自動流程 PAT item與BIN與MSEC談好 PAT item VBDSS,VGS .PAT FAL BIN :21 \n18. MOS 建立流程/新人MOS學習參考 , 1.Wsstest文件原始檔MSEC_MOS_委外相關資訊V1.0.xlsx =\n19. Fab MAP相關規範 , 1.於 2025/4/9 (週三) 下午 04:48 MALI主旨:FW: G3730AE 測試資料 MAIL內容: 理論上我們是不需再提供map 給外測廠. '\n20. MDS1527 流程 , 1. 原廠map(缺口下) 轉90度 為了 prober手臂不轉(轉會破片) (CP1) 2. CP1 測試UIS全部die (CP1 fail bin 33~64 原廠 fail bin 1~32)3. marge map CP1 + 原廠map marge rule (2025/4/18 mail to Kevin.", "metadata": {"chunk_id": 7, "start_char": 2414, "has_overlap": true, "created_time": "2025-08-29T22:24:51.826609", "file_size": 7437, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_name": "部門rule宣導_20250509.md", "end_char": 2933}, "distance": 0.5429896712303162, "source": ""}, {"id": "chunk_8", "text": "e (CP1 fail bin 33~64 原廠 fail bin 1~32)3. marge map CP1 + 原廠map marge rule (2025/4/18 mail to <PERSON><PERSON><PERSON>)4. MDS1527合併流程: C:UsersAkiraLinOneDriveMDS1527合併Merge 分Bin_20250417_最終合併流程.xlsx \n21. 華天西安 hold lot網頁反饋作業 , 華天西安廠推出hold lot 於網頁上直接處理。免除mail來往及放長假忘記回覆，或華天對應人員離職而未處理。操作指導書如附件。雙週會會為各位解說示範操作。C:UsersAkiraLinOneDrive文件6.AI訓練資料基本RULE判斷1.0文件原始檔Hold Lot网?化反?作?指?-客?.pptx \n測試機=測試機代碼 \nASL-X=AX \nASL-1K V4.82(NT)=T \nASL-1K V6.3(XP=TX \nT861=H \nCTA-8280H=CTA \nCTA-8280F=CTAF \nCTA-8290D=CTAX \nCTA-8290DPlus=CTAX", "metadata": {"created_time": "2025-08-29T22:24:51.826609", "file_name": "部門rule宣導_20250509.md", "file_size": 7437, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "has_overlap": true, "chunk_id": 8, "end_char": 3333, "start_char": 2833}, "distance": 0.5697169303894043, "source": ""}, {"id": "chunk_2", "text": ":4塊3.Friday, October 18, 2024 9:20 AM 由Xu Yongyu 徐永雨mail通知,mail主旨:G948程式配件寄送 \n6.MSEC_CP_小die的限制 , 1.一般 Prober 限制 :Die size (含 scribe line) 必須 >500×500 μm，僅適用於 6吋產品。2.MSEC Prober 限制:UF200A (8吋) >320×320 μm，UF200 (8吋) 500×800 μm。3.GMT Prober 限制:受機台型號及軟體版本影響，UF200 無法測試 >300×300 μm。4.小 Die Alignment 問題:<500×500 μm 時，Alignment 經常失敗，需手動調整，存在風險。5.ESI9350 系列 (Laser 加工能力):最大生產數量：45000 ea；Fuse 根數限制：20000-30000 根。6.ESI9830 系列 (Laser 加工能力):最大生產數量：100000 ea；Fuse 根數尚未遇到問題。7.Fuse Process 限制:單次 process Fuse 根數須低於 20000-30000 根，超過可能導致問題。", "metadata": {"file_size": 7437, "chunk_id": 2, "created_time": "2025-08-29T22:24:51.826609", "end_char": 926, "has_overlap": true, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_name": "部門rule宣導_20250509.md", "start_char": 400}, "distance": 0.5698552131652832, "source": ""}, {"id": "chunk_1", "text": "1.CTA & CTAF 價格資訊 , 1.在大陸地區，CTA 與 CTAF 每小時價格相同，資訊來源 PC-IVY。詳情需洽 PC-BOSS，但無法進一步提供。 \n2.IC 壓塊規範 , 1.需經 TE 驗證，確保測試環境相符。2.尺寸：需符合 IC 大小。3.壓頭：空心設計，金屬材質。 \n3.WLCSP Frame Prober 測試使用時機 , 1.針對切割後產生 crack 並已有客訴的特定 WLCSP 產品，才開啟 Frame Prober 測試。一般情況一律不使用，避免額外成本與流程。 \n4.Release Form 與 DLL 時間規則 , 1.DLL 時間應早於或不超過 Release Form 1 天；否則視為異常，需重新 Release。確保程式發行與 DLL 版本完全匹配，避免未正式 Release 的修改。 \n5.滁州) 標準配置 , 1.FOVI:4塊2.FPVI:4塊3.Friday, October 18, 2024 9:20 AM 由Xu Yongyu 徐永雨mail通知,mail主旨:G948程式配件寄送 \n6.MSEC_CP_小die的限制 , 1.", "metadata": {"created_time": "2025-08-29T22:24:51.826609", "file_size": 7437, "has_overlap": false, "chunk_id": 1, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "end_char": 500, "file_name": "部門rule宣導_20250509.md", "start_char": 0}, "distance": 0.5717675685882568, "source": ""}, {"id": "chunk_3", "text": "加工能力):最大生產數量：100000 ea；Fuse 根數尚未遇到問題。7.Fuse Process 限制:單次 process Fuse 根數須低於 20000-30000 根，超過可能導致問題。 \n7.CST Socket Repeat Order 流程 , 步驟1: PC產品負責人提出mail確認handler type (P&P or 轉塔機).步驟2:TE產品負責人Confirm handler type後回信.步驟3:PC(Andy)負責提出請購需求給CST.步驟4:CST回復報價與交期. , \n8.模組化清單,模組查詢,關於跨組模組 , 1.如果工程師有新產品跟其他組類似,例如我們有接到power switch,就需要與他組相關人員討論, 使用哪一套配件開發.2.目前MOTOR系列使用LOWPIN board 規劃3.模組化清單路徑 wssTEST測試產品模組暨測試開發參考測試模組化清單.xlsx \n9.M8系模組 , 1.未來新案皆使用LOWPIN Board.2.Tony有 UR commom code3.20250303有宣導mail主旨: RE: 宣導-模組資訊參考 \n11.", "metadata": {"has_overlap": true, "start_char": 826, "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "created_time": "2025-08-29T22:24:51.826609", "chunk_id": 3, "end_char": 1333, "file_name": "部門rule宣導_20250509.md", "file_size": 7437}, "distance": 0.579529881477356, "source": ""}, {"id": "chunk_9", "text": "(NT)=T \nASL-1K V6.3(XP=TX \nT861=H \nCTA-8280H=CTA \nCTA-8280F=CTAF \nCTA-8290D=CTAX \nCTA-8290DPlus=CTAXP \nAST-2000=AST \nSTS-8200=STS \nSTS-8202=STS \nETS-series=E \nS100=YS \n測試廠=測試廠代碼 \n逸昌=ETD \n超豐=GTK \n微矽=MSEC \n菱生=LS\n南岩=NANO\n久元=YTEC\n長電先進 , JCAP \n長電 , JCET \n通富微 , TFME \n天水華天 , TSHT \n西安華天 , XAHT \n江蘇芯德 , JSSI \n測試程式&治具命名規則 \nTEMP & ENG 程式使用規定 , 1. 適用範圍 :a. TEMP專為 Re-screen良品所使用(重測完才可出貨), 需要上系統正式 release且使用專用型號。(非必要性測項須刪減或減少測試流程以減少測試成本)。如為特殊緣故須使用完整版測試程式重新 Re-screen 良品則採用正式版程式，不適用TEMP。b.", "metadata": {"created_time": "2025-08-29T22:24:51.826609", "chunk_id": 9, "end_char": 3710, "start_char": 3233, "has_overlap": true, "file_name": "部門rule宣導_20250509.md", "file_path": "/mnt/d/project/python/tongrag3/doc/部門rule宣導_20250509.md", "file_size": 7437}, "distance": 0.586730420589447, "source": ""}], "count": 10}}, "summary": {"total_methods": 3, "successful_methods": 3}}