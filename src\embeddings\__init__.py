"""
嵌入模型管理模組

該模組負責管理 BGE-M3 嵌入模型的生命週期，提供預載入功能
以避免首次搜索時的載入延遲。
"""

import os
import logging
from typing import Optional
import chromadb.utils.embedding_functions as embedding_functions

logger = logging.getLogger(__name__)

# 全局變數用於快取嵌入函數
_embedding_function_cache: Optional[embedding_functions.SentenceTransformerEmbeddingFunction] = None


def get_embedding_function(model_name: str = None) -> embedding_functions.SentenceTransformerEmbeddingFunction:
    """
    獲取嵌入函數實例，支援快取以避免重複載入。
    
    Args:
        model_name: 嵌入模型名稱，默認使用配置中的模型
        
    Returns:
        ChromaDB SentenceTransformerEmbeddingFunction 實例
    """
    global _embedding_function_cache
    
    if model_name is None:
        model_name = os.getenv("EMBEDDING_MODEL", "BAAI/bge-m3")
    
    # 如果已經快取且模型名稱相同，直接返回
    if _embedding_function_cache is not None:
        if hasattr(_embedding_function_cache, '_model_name') and _embedding_function_cache._model_name == model_name:
            logger.debug(f"使用快取的嵌入函數: {model_name}")
            return _embedding_function_cache
    
    try:
        logger.info(f"載入嵌入模型: {model_name}")
        embedding_function = embedding_functions.SentenceTransformerEmbeddingFunction(
            model_name=model_name
        )
        
        # 添加模型名稱屬性以便後續檢查
        embedding_function._model_name = model_name
        
        # 快取函數實例
        _embedding_function_cache = embedding_function
        
        logger.info(f"嵌入模型載入完成: {model_name}")
        return embedding_function
        
    except Exception as e:
        logger.error(f"載入嵌入模型失敗 {model_name}: {str(e)}")
        raise


def preload_embedding_model(model_name: str = None) -> bool:
    """
    預載入嵌入模型，在程式啟動時調用以避免首次搜索延遲。
    
    Args:
        model_name: 要預載入的模型名稱
        
    Returns:
        bool: 是否成功載入
    """
    try:
        # 檢查是否啟用預載入
        preload_enabled = os.getenv("PRELOAD_EMBEDDING_MODEL", "true").lower() in ("true", "1", "yes")
        
        if not preload_enabled:
            logger.info("嵌入模型預載入已禁用")
            return False
        
        logger.info("開始預載入嵌入模型...")
        
        # 獲取嵌入函數（這會觸發模型載入）
        embedding_function = get_embedding_function(model_name)
        
        # 進行一次小型的模型預熱，確保完全載入
        test_text = ["測試文本"]
        _ = embedding_function(test_text)
        
        logger.info("嵌入模型預載入完成！")
        return True
        
    except Exception as e:
        logger.warning(f"嵌入模型預載入失敗: {str(e)}")
        return False


def is_model_loaded() -> bool:
    """
    檢查嵌入模型是否已載入。
    
    Returns:
        bool: 模型是否已載入
    """
    return _embedding_function_cache is not None