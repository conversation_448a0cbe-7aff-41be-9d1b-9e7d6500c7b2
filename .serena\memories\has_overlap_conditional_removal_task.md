# Has_Overlap Conditional Logic Removal Task

## Task Context
User discovered remaining conditional logic in display.py that still uses different display formats based on `has_overlap` status. This violates the requirement to completely remove all old format code and force uniform overlap format display.

## Issues Found

### 1. src/ui/ui_search/display.py - Lines 264-276
```python
# 根據是否有重疊使用不同的標記和顏色
if has_overlap:
    overlap_marker = " 🔗"
    title_color = "yellow"
    self.print_colored(f"[{rank}]{overlap_marker} 相關度: {score} | 來源: {file_name}", title_color)
    
    # 顯示重疊來源信息
    overlap_source_info = result.get("overlap_source_info", "")
    if overlap_source_info:
        self.print_colored(f"    🔗 [重疊區域]", "cyan")
        print(f"       來源塊: {overlap_source_info}")
else:
    title_color = "green"
    self.print_colored(f"[{rank}] 相關度: {score} | 來源: {file_name}", title_color)
```

### 2. src/ui/ui_search/display.py - Lines 353-365
```python
# 根據是否有重疊使用不同的標記和顏色
if has_overlap:
    overlap_marker = " 🔗"
    title_color = "yellow"
    self.print_colored(f"[{rank}]{rank_indicator}{overlap_marker} 相關度: {score} | 來源: {file_name}", title_color)
    
    # 顯示重疊來源信息
    overlap_source_info = result.get("overlap_source_info", "")
    if overlap_source_info:
        self.print_colored(f"    🔗 [重疊區域]", "cyan")
        print(f"       來源塊: {overlap_source_info}")
else:
    title_color = "green"
    self.print_colored(f"[{rank}]{rank_indicator} 相關度: {score} | 來源: {file_name}", title_color)
```

### 3. src/ui/ui_search/display.py - Line 442
```python
# 如果有重疊檢測結果，顯示重疊統計
if result.get('has_overlap', False):
```

## Required Changes

### 1. Remove Conditional Logic in display_single_result
- Remove `if has_overlap:` and `else:` branches
- Force all results to use yellow color and 🔗 marker
- Always show overlap source info section (even if empty)

### 2. Remove Conditional Logic in display_single_result_with_changes  
- Same changes as above but in the with_changes variant
- Maintain rank indicators while removing has_overlap conditions

### 3. Remove Conditional Check in show_full_content
- Always show overlap statistics section regardless of has_overlap status
- Force display of overlap information for all results

## User Requirements
- "把所有會用到舊格式的都刪了" (Delete all old format code)
- Force uniform overlap format for ALL results
- Preserve debug logging
- No fallback mechanisms

## Success Criteria
1. All results display with identical format (🔗 marker, yellow color)
2. No conditional branches based on has_overlap exist
3. System tested with both "test" and "msec cp" queries show identical format
4. Debug logs preserved and functional

## Technical Notes
- Content display already forced to use `_display_overlap_sections()`
- Only title/header display logic needs modification
- Must preserve existing functionality while removing conditions