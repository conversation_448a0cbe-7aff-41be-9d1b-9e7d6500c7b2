{"permissions": {"allow": ["Bash(find:*)", "Bash(ls:*)", "Bash(claude --version)", "<PERSON><PERSON>(python:*)", "Bash(pip3 install:*)", "<PERSON><PERSON>(source:*)", "Bash(ss:*)", "<PERSON><PERSON>(sudo netstat:*)", "Bash(grep:*)", "Bash(ln:*)", "<PERSON><PERSON>(curl:*)", "Bash(kill:*)", "<PERSON><PERSON>(pkill:*)", "<PERSON><PERSON>(timeout:*)", "<PERSON><PERSON>(cat:*)", "Bash(rm:*)", "Bash(git add:*)", "Bash(git reset:*)", "Bash(git push:*)", "<PERSON><PERSON>", "Create", "Edit", "Read", "Write", "mcp__Context7__*", "mcp__mcp-deepwiki__*", "mcp__sequential-thinking__*", "WebFetch(domain:cdnjs.cloudflare.com)", "WebFetch(domain:docs.anthropic.com)", "mcp__sequential-thinking__sequentialthinking", "mcp__Context7__resolve-library-id", "mcp__Context7__get-library-docs", "mcp__mcp-deepwiki__deepwiki_fetch", "WebFetch(domain:localhost)", "WebFetch(domain:docs.openwebui.com)", "WebFetch(domain:************)", "WebFetch(domain:qwenlm.github.io)", "WebFetch(domain:deepwiki.com)", "WebFetch(domain:github.com)", "mcp__serena__check_onboarding_performed", "mcp__serena__activate_project", "mcp__serena__onboarding", "WebSearch", "mcp__serena__read_memory", "WebFetch(domain:www.andreagrandi.it)", "WebFetch(domain:gist.github.com)", "mcp__serena__list_dir", "mcp__serena__list_memories", "mcp__playwright__playwright_navigate", "mcp__serena__find_symbol", "mcp__serena__search_for_pattern", "mcp__serena__find_file", "mcp__serena__get_symbols_overview", "mcp__serena__write_memory", "mcp__serena__think_about_collected_information", "mcp__serena__replace_symbol_body", "mcp__serena__think_about_task_adherence"], "deny": []}, "statusLine": {"type": "command", "command": "bash /mnt/d/project/python/tongrag3/.claude/statusline-enhanced.sh"}, "outputStyle": "serena-orchestrator"}