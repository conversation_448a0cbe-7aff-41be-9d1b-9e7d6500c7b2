# TongRAG3 技術架構文檔

> **TongRAG3 v0.2.0** - 智能文檔檢索系統技術架構說明

## 📋 目錄

- [系統概覽](#系統概覽)
- [核心架構](#核心架構)
- [搜索結果重疊機制](#搜索結果重疊機制)
- [重排序引擎](#重排序引擎)
- [對比分析系統](#對比分析系統)
- [模組組織](#模組組織)
- [資料流程](#資料流程)
- [技術選型](#技術選型)
- [配置系統](#配置系統)
- [性能優化](#性能優化)
- [擴展性設計](#擴展性設計)

## 🎯 系統概覽

TongRAG3 是一個基於向量檢索技術的智能文檔檢索系統，採用模組化設計，集成了先進的 BGE 重排序引擎和多維度對比分析功能。

### 核心特性

```mermaid
graph TB
    A[文檔輸入] --> B[文檔處理器]
    B --> C[向量化引擎]
    C --> D[ChromaDB向量庫]
    E[用戶查詢] --> F[搜索引擎]
    F --> G[初始檢索]
    G --> H{啟用重排序?}
    H -->|是| I[BGE重排序引擎]
    H -->|否| J[返回結果]
    I --> K[重排序結果]
    K --> L[UI顯示]
    J --> L
    L --> M[對比分析模式]
```

## 🏗️ 核心架構

### 系統分層

```
┌─────────────────────────────────────┐
│           使用者介面層 (UI Layer)      │
├─────────────────────────────────────┤
│         應用服務層 (Service Layer)    │
├─────────────────────────────────────┤
│         搜索引擎層 (Engine Layer)     │
├─────────────────────────────────────┤
│         資料存取層 (Data Layer)       │
└─────────────────────────────────────┘
```

#### 1. 使用者介面層 (UI Layer)

負責與使用者的所有互動，採用模組化設計：

```python
src/ui/
├── __init__.py          # UI模組整合器
├── ui_base.py          # 基礎UI功能
├── ui_menu.py          # 選單和導航
├── ui_search.py        # 搜索結果顯示
├── ui_comparison.py    # 對比分析介面
├── ui_tutorials.py     # 教學文檔
└── ui_system.py        # 系統管理
```

**設計原則：**
- 單一職責：每個UI模組只負責特定功能
- 模組間低耦合，通過繼承實現功能整合
- 統一的顏色和樣式管理
- 響應式分頁和錯誤處理

#### 2. 應用服務層 (Service Layer)

提供業務邏輯和工具函數：

```python
src/
├── config.py           # 配置管理服務
├── utils.py            # 工具函數集合
├── document_loader.py  # 文檔處理服務
└── comparison.py       # 對比分析服務（預留）
```

#### 3. 搜索引擎層 (Engine Layer)

系統的核心，包含所有檢索相關功能：

```python
# src/search_engine.py
class ChromaSearchEngine:
    # 基礎搜索功能
    def semantic_search()      # 語義搜索
    def keyword_search()       # 關鍵詞搜索
    def hybrid_search()        # 混合搜索
    def metadata_search()      # 元數據搜索
    def regex_search()         # 正則表達式搜索
    
    # 重排序功能
    def rerank_results()       # BGE重排序引擎
    def _init_reranker()       # 重排序模型初始化
    def _calculate_rerank_scores()  # 重排序分數計算
```

**搜索結果重疊機制架構**

搜索引擎整合了智能重疊檢測和顯示系統：

```mermaid
graph TD
    A[文檔載入] --> B[智能分塊]
    B --> C[重疊檢測]
    C --> D[元數據生成]
    D --> E[向量存儲]
    
    F[搜索查詢] --> G[向量檢索]
    G --> H[結果格式化]
    H --> I[重疊信息提取]
    I --> J[兩級顯示]
    
    J --> K[簡潔列表]
    J --> L[詳細查看]
    
    C --> M[位置計算]
    C --> N[內容分離]
    M --> O[重疊百分比]
    N --> P[文本標記]
```

**重疊顯示核心組件**：

```python
# src/document_loader.py - 重疊檢測
def split_text(self, text: str, file_path: str) -> List[Dict]:
    """
    智能文檔分塊與重疊檢測
    
    核心功能：
    - 基於實際位置的重疊檢測算法
    - 精確的文本分離和標記
    - 完整的元數據生成
    """
    positions = self._calculate_chunk_positions(text)
    
    for i, (start_pos, end_pos) in enumerate(positions):
        if i > 0 and start_pos < positions[i-1][1]:
            # 檢測到實際重疊
            has_overlap = True
            overlap_size = positions[i-1][1] - start_pos
            overlap_text = text[start_pos:positions[i-1][1]]
            new_content = text[positions[i-1][1]:end_pos]

# src/utils/search.py - 結果格式化
def format_results(results: List[Dict]) -> List[Dict]:
    """
    增強的結果格式化與重疊信息處理
    
    核心功能：
    - 從元數據提取重疊信息
    - 智能文本清理和分離
    - 向後兼容舊版本數據
    """
    for result in results:
        metadata = result.get("metadata", {})
        has_overlap = metadata.get("has_overlap", False)
        overlap_text = clean_text(metadata.get("overlap_text", ""))
        new_content = clean_text(metadata.get("new_content", ""))

# src/ui/ui_search/display.py - 兩級顯示
class DisplayMixin(BaseUI):
    """
    智能重疊顯示系統
    
    顯示策略：
    Level 1: 簡潔列表 - 重疊部分(完整) + 新內容(截斷)
    Level 2: 詳細查看 - 重疊分析 + 完整內容展示
    """
    def display_single_result(self, result):
        if result.get("has_overlap"):
            self._display_overlap_result(result)
        else:
            self._display_unique_result(result)
    
    def show_full_content(self, result):
        if result.get("has_overlap"):
            self._show_overlap_analysis(result)
        self._show_complete_content(result)
```

#### 4. 資料存取層 (Data Layer)

負責與向量資料庫的互動：

- **ChromaDB**: 向量存儲和相似度檢索
- **本地檔案系統**: 文檔存儲和日誌管理
- **配置檔案**: 系統參數和環境變數

## 🔍 搜索結果重疊機制

### 重疊機制概覽

TongRAG3 v0.2.1 引入了智能搜索結果重疊檢測和顯示機制，解決了文檔分塊過程中重疊內容的準確識別和用戶友好顯示問題。

```mermaid
flowchart LR
    A[文檔分塊] --> B[重疊檢測算法]
    B --> C[元數據生成]
    C --> D[格式化處理]
    D --> E[兩級顯示]
    
    B --> F[位置計算]
    B --> G[內容分離]
    F --> H[重疊百分比]
    G --> I[文本標記]
    
    E --> J[簡潔列表視圖]
    E --> K[詳細分析視圖]
```

### 核心技術特點

#### 1. 精確重疊檢測

基於實際字符位置的數學計算，而非簡單的配置參數：

```python
# 關鍵算法：實際重疊檢測
def detect_actual_overlap(current_chunk, previous_chunk):
    if current_chunk.start_pos < previous_chunk.end_pos:
        overlap_size = previous_chunk.end_pos - current_chunk.start_pos
        overlap_text = original_text[current_chunk.start_pos:previous_chunk.end_pos]
        new_content = original_text[previous_chunk.end_pos:current_chunk.end_pos]
        return True, overlap_size, overlap_text, new_content
    return False, 0, "", current_chunk.text
```

#### 2. 智能內容分離

將每個文檔塊的內容精確分離為重疊部分和新內容：

- **重疊部分**: 與前一個文檔塊相同的文本內容
- **新內容**: 該文檔塊獨有的文本內容
- **重疊百分比**: 重疊內容占整個文檔塊的百分比

#### 3. 兩級漸進式顯示

```
Level 1 - 搜索列表（簡潔視圖）：
┌─────────────────────────────────────┐
│ 🟨 重疊部分: [完整顯示重疊文本]      │
│ 🟩 新內容: [截斷顯示] (還有X個字)    │
└─────────────────────────────────────┘

Level 2 - 詳細查看（完整視圖）：
┌─────────────────────────────────────┐
│ 📊 重疊分析: 大小、百分比、統計      │
│ 🟨 重疊部分: [完整重疊文本]         │
│ 🟩 新內容: [完整新內容文本]         │
│ 📄 完整內容: [整個文檔塊內容]       │
└─────────────────────────────────────┘
```

### 架構層次整合

#### 文檔處理層整合

```python
# src/document_loader.py
class DocumentLoader:
    def split_text(self, text: str) -> List[Dict]:
        """
        增強的文檔分塊功能
        
        新增特性：
        1. 位置預計算 - 先計算所有塊位置
        2. 實際重疊檢測 - 基於位置而非配置
        3. 精確內容分離 - 數學計算重疊和新內容
        4. 完整元數據生成 - 包含所有重疊信息
        """
        positions = self._calculate_chunk_positions(text)
        
        for i, (start_pos, end_pos) in enumerate(positions):
            if i > 0:  # 檢查與前一塊的重疊
                prev_end = positions[i-1][1]
                has_overlap = start_pos < prev_end
                
                if has_overlap:
                    overlap_size = prev_end - start_pos
                    overlap_text = text[start_pos:prev_end]
                    new_content = text[prev_end:end_pos].strip()
                    overlap_percentage = (overlap_size / len(chunk_text)) * 100
```

#### 結果格式化層整合

```python
# src/utils/search.py
def format_results(results: List[Dict]) -> List[Dict]:
    """
    增強的結果格式化
    
    重疊機制整合：
    1. 元數據提取 - 從存儲中提取重疊信息
    2. 向後兼容 - 處理舊版本數據
    3. 文本清理 - 統一文本格式化
    4. 顯示準備 - 為兩級顯示準備數據
    """
    for result in results:
        metadata = result.get("metadata", {})
        
        # 提取重疊信息
        has_overlap = metadata.get("has_overlap", False)
        overlap_text = metadata.get("overlap_text", "")
        new_content = metadata.get("new_content", "")
        
        # 向後兼容處理
        if has_overlap and not overlap_text:
            # 對舊數據進行兼容性重構
            overlap_size = metadata.get("overlap_size", 0)
            if overlap_size > 0:
                full_text = clean_text(result.get("text", ""))
                overlap_text = full_text[:overlap_size]
                new_content = full_text[overlap_size:]
```

#### UI顯示層整合

```python
# src/ui/ui_search/display.py
class DisplayMixin:
    def display_single_result(self, result: Dict) -> None:
        """
        智能單結果顯示
        
        顯示邏輯：
        1. 檢查重疊狀態
        2. 選擇適當的顯示模式
        3. 應用顏色和圖標標記
        4. 提供剩餘字數信息
        """
        if result.get("has_overlap", False):
            self._display_overlap_result(result)
        else:
            self._display_unique_result(result)
    
    def show_full_content(self, result: Dict) -> None:
        """
        詳細內容顯示
        
        分析展示：
        1. 重疊統計分析
        2. 分段內容顯示
        3. 完整內容提供
        4. 互動式導航
        """
        if result.get("has_overlap"):
            self._show_overlap_analysis(result)
            self._show_overlap_sections(result)
        self._show_complete_content(result)
```

### 數據流程整合

重疊機制與現有系統的數據流程完全整合：

```mermaid
sequenceDiagram
    participant U as 用戶
    participant UI as 界面層
    participant SE as 搜索引擎
    participant SF as 格式化器
    participant VDB as 向量庫
    participant DL as 文檔加載器
    
    Note over DL: 文檔處理階段
    DL->>DL: 智能分塊
    DL->>DL: 重疊檢測
    DL->>DL: 元數據生成
    DL->>VDB: 存儲帶重疊信息的塊
    
    Note over U,VDB: 搜索執行階段  
    U->>UI: 輸入查詢
    UI->>SE: 搜索請求
    SE->>VDB: 向量檢索
    VDB-->>SE: 返回結果(含重疊元數據)
    SE->>SF: 結果格式化
    SF->>SF: 重疊信息提取和處理
    SF-->>SE: 格式化結果
    SE-->>UI: 最終結果
    
    Note over UI,U: 顯示階段
    UI->>UI: Level 1 簡潔顯示
    UI-->>U: 搜索列表(重疊標記)
    U->>UI: 請求詳細查看
    UI->>UI: Level 2 詳細分析
    UI-->>U: 完整重疊分析
```

### 向後兼容架構

重疊機制設計了完整的向後兼容架構：

```python
class BackwardCompatibilityLayer:
    """向後兼容處理層"""
    
    def handle_legacy_data(self, result):
        """處理舊版本數據"""
        metadata = result.get("metadata", {})
        
        # 檢查是否有新字段
        if not self._has_overlap_fields(metadata):
            # 舊數據兼容：標記為無重疊
            return self._create_legacy_compatible_result(result)
        
        # 新數據直接使用
        return result
    
    def _has_overlap_fields(self, metadata):
        """檢查是否包含重疊字段"""
        required_fields = ['has_overlap', 'overlap_text', 'new_content']
        return all(field in metadata for field in required_fields)
```

### 性能優化架構

重疊機制包含多層性能優化：

```python
class OverlapPerformanceOptimizer:
    """重疊機制性能優化器"""
    
    def __init__(self):
        self.position_cache = LRUCache(maxsize=1000)
        self.overlap_cache = LRUCache(maxsize=500)
    
    def optimize_overlap_detection(self):
        """優化重疊檢測性能"""
        # 1. 位置計算快取
        # 2. 重疊結果快取  
        # 3. 批次處理優化
        # 4. 懶加載機制
```

詳細的重疊機制技術說明請參考：[docs/OVERLAP_MECHANISM.md](docs/OVERLAP_MECHANISM.md)

## 🤖 重排序引擎

### BGE 重排序架構

```mermaid
sequenceDiagram
    participant U as 用戶查詢
    participant S as 搜索引擎
    participant V as 向量檢索
    participant R as BGE重排序
    participant D as 結果顯示
    
    U->>S: 提交查詢
    S->>V: 執行向量檢索
    V-->>S: 返回候選結果
    S->>R: 發送查詢+結果
    R->>R: 計算相關性分數
    R-->>S: 返回重排序結果
    S-->>D: 格式化並顯示
```

### 重排序實現細節

#### 1. 模型初始化

```python
def _init_reranker(self) -> None:
    """初始化BGE重排序模型"""
    try:
        from sentence_transformers import CrossEncoder
        model_name = "BAAI/bge-reranker-base"
        self.reranker = CrossEncoder(model_name)
        self.logger.info(f"BGE重排序模型初始化成功: {model_name}")
    except Exception as e:
        self.logger.error(f"重排序模型初始化失敗: {e}")
        self.reranker = None
```

#### 2. 重排序處理流程

```python
def rerank_results(self, query: str, results: List[Dict]) -> List[Dict]:
    """
    重排序流程：
    1. 驗證輸入和模型狀態
    2. 構建查詢-文檔對
    3. BGE模型計算相關性分數
    4. 根據分數重新排序
    5. 應用閾值過濾
    6. 返回最終結果
    """
```

#### 3. 性能優化策略

- **批次處理**: 一次處理多個查詢-文檔對
- **內存管理**: 及時釋放模型推理過程中的中間結果
- **結果快取**: 對相同查詢的結果進行快取
- **閾值過濾**: 過濾低相關性結果，減少輸出噪音

## 📊 對比分析系統

### 對比分析架構

```mermaid
graph LR
    A[對比請求] --> B{對比類型}
    B --> C[搜索方法對比]
    B --> D[重排序效果對比]
    B --> E[批量查詢對比]
    
    C --> F[語義搜索]
    C --> G[關鍵詞搜索]
    C --> H[混合搜索]
    
    D --> I[原始結果]
    D --> J[重排序結果]
    
    F --> K[結果分析]
    G --> K
    H --> K
    I --> L[排名變化分析]
    J --> L
    
    K --> M[可視化展示]
    L --> M
    M --> N[報告導出]
```

### 對比功能模組

#### 1. 並排對比顯示

```python
def display_rerank_comparison_results(self, query: str, 
                                    original_results: List[Dict], 
                                    reranked_results: List[Dict]) -> None:
    """
    並排顯示原始結果 vs 重排序結果
    包含：
    - 基本統計信息
    - 排名變化分析
    - 視覺化變化指示
    - 分頁展示控制
    """
```

#### 2. 排名變化追蹤

```python
def _calculate_comprehensive_ranking_changes(self, 
                                           original_results: List[Dict], 
                                           reranked_results: List[Dict]) -> Dict:
    """
    計算全面的排名變化統計：
    - 提升的結果數量
    - 下降的結果數量
    - 不變的結果數量
    - 被過濾的結果數量
    - 詳細的位置變化映射
    """
```

#### 3. 改進指標計算

```python
def calculate_improvement_metrics(self, original_results: List[Dict], 
                                reranked_results: List[Dict]) -> None:
    """
    計算量化改進指標：
    - 結果保留率
    - 平均分數改進
    - Top-K準確度
    - 多樣性指標
    """
```

## 📁 模組組織

### 目錄結構詳解

```
tongrag3/
├── main.py                    # 程序入口點
├── src/                       # 核心源代碼
│   ├── __init__.py           # 包初始化
│   ├── config.py             # 配置管理
│   ├── search_engine.py      # 搜索引擎核心
│   ├── document_loader.py    # 文檔處理器
│   ├── utils.py              # 工具函數
│   ├── comparison.py         # 對比分析引擎（預留）
│   ├── ui.py                 # UI模組入口
│   └── ui/                   # UI子模組包
│       ├── __init__.py       # UI整合器
│       ├── ui_base.py        # 基礎功能
│       ├── ui_menu.py        # 選單導航
│       ├── ui_search.py      # 搜索結果
│       ├── ui_comparison.py  # 對比分析
│       ├── ui_tutorials.py   # 教學文檔
│       └── ui_system.py      # 系統管理
├── doc/                      # 文檔目錄
├── chroma_data/              # 向量資料庫
├── logs/                     # 系統日誌
├── exports/                  # 導出結果
└── docs/                     # 項目文檔
    ├── README.md            # 項目說明
    ├── CHANGELOG.md         # 變更日誌
    ├── ARCHITECTURE.md      # 架構文檔（本文件）
    ├── RERANKING_GUIDE.md   # 重排序使用指南
    └── COMPARISON_GUIDE.md  # 對比功能指南
```

### 模組相依性

```mermaid
graph TD
    A[main.py] --> B[src.ui.TongRAGInterface]
    B --> C[src.search_engine.ChromaSearchEngine]
    B --> D[src.ui.MenuUI]
    B --> E[src.ui.SearchUI]
    B --> F[src.ui.ComparisonUI]
    
    C --> G[src.config]
    C --> H[src.utils]
    C --> I[ChromaDB]
    C --> J[sentence_transformers]
    
    E --> K[src.ui.BaseUI]
    F --> K
    D --> K
    
    F --> C
    F --> L[src.comparison]
```

## 🔄 資料流程

### 1. 標準搜索流程

```mermaid
sequenceDiagram
    participant U as 用戶
    participant UI as 界面層
    participant SE as 搜索引擎
    participant VDB as 向量資料庫
    
    U->>UI: 輸入查詢
    UI->>UI: 驗證輸入
    UI->>SE: 發送搜索請求
    SE->>VDB: 向量檢索
    VDB-->>SE: 返回候選結果
    SE-->>UI: 格式化結果
    UI-->>U: 顯示搜索結果
```

### 2. 重排序搜索流程

```mermaid
sequenceDiagram
    participant U as 用戶
    participant UI as 界面層
    participant SE as 搜索引擎
    participant VDB as 向量資料庫
    participant BGE as BGE重排序
    
    U->>UI: 選擇重排序搜索
    UI->>SE: 發送重排序搜索請求
    SE->>VDB: 向量檢索（Top-15）
    VDB-->>SE: 返回候選結果
    SE->>BGE: 查詢+候選結果
    BGE->>BGE: 計算相關性分數
    BGE-->>SE: 返回重排序結果
    SE-->>UI: 格式化最終結果
    UI-->>U: 顯示重排序結果
```

### 3. 對比分析流程

```mermaid
sequenceDiagram
    participant U as 用戶
    participant CUI as 對比界面
    participant SE as 搜索引擎
    participant CA as 對比分析器
    
    U->>CUI: 啟動對比模式
    CUI->>SE: 執行多種搜索
    SE-->>CUI: 返回各種結果
    CUI->>CA: 發送對比數據
    CA->>CA: 計算變化統計
    CA->>CA: 生成改進指標
    CA-->>CUI: 返回分析結果
    CUI-->>U: 視覺化顯示對比
```

## 🛠️ 技術選型

### 核心技術棧

| 組件 | 技術選型 | 版本要求 | 選擇理由 |
|------|----------|----------|----------|
| **向量資料庫** | ChromaDB | >= 0.4.0 | 輕量級、本地部署、支援多種檢索模式 |
| **嵌入模型** | BAAI/bge-m3 | latest | 多語言支持、高質量中文嵌入 |
| **重排序模型** | BAAI/bge-reranker-base | latest | 專業重排序、高精度相關性計算 |
| **文檔處理** | PyPDF2 + python-docx | latest | 多格式文檔解析 |
| **UI框架** | Colorama | >= 0.4.0 | 跨平台命令行顏色支持 |
| **配置管理** | 環境變數 + YAML | - | 靈活配置、易於部署 |

### 依賴關係

```yaml
必需依賴:
  - chromadb: 向量數據庫核心
  - pandas: 數據處理
  - numpy: 數值計算
  - sentence-transformers: 嵌入和重排序模型

可選依賴:
  - colorama: 終端顏色支持
  - python-docx: Word文檔處理  
  - PyPDF2: PDF文檔處理

開發依賴:
  - pytest: 單元測試
  - black: 代碼格式化
  - mypy: 類型檢查
```

## ⚙️ 配置系統

### 配置層次結構

```
環境變數 (最高優先級)
    ↓
配置文件 (config.yaml)
    ↓  
默認配置 (config.py)
```

### 主要配置項

```python
# 基礎配置
DEFAULT_N_RESULTS = 8        # 默認搜索結果數量
PAGE_SIZE = 5                # 每頁顯示結果數
CHUNK_SIZE = 500             # 文檔分塊大小
COLLECTION_NAME = "default"   # 集合名稱

# 重排序配置
RERANK_ENABLED = True        # 啟用重排序
RERANK_TOP_K = 15           # 重排序候選數量
RERANK_THRESHOLD = 0.1      # 重排序分數閾值

# 對比配置
COMPARISON_MODE = True       # 啟用對比模式
BATCH_SIZE = 10             # 批量處理大小

# 性能配置
MAX_WORKERS = 4             # 最大工作線程
CACHE_SIZE = 1000           # 結果快取大小
```

### 環境變數覆蓋

```bash
# 搜索配置
export DEFAULT_N_RESULTS=15
export PAGE_SIZE=8

# 重排序配置  
export RERANK_TOP_K=20
export RERANK_THRESHOLD=0.05

# 啟動系統
python main.py
```

## 🚀 性能優化

### 1. 重排序性能優化

#### 模型加載優化
```python
# 懶加載：只在需要時初始化模型
def _init_reranker(self):
    if self.reranker is None:
        # 模型初始化邏輯
        pass
```

#### 批次處理優化
```python
# 批次重排序，減少模型調用次數
def rerank_batch(queries: List[str], results: List[List[Dict]]):
    # 批次處理邏輯
    pass
```

### 2. 內存管理優化

- **結果快取**: LRU快取策略，避免重複計算
- **模型復用**: 單例模式管理重排序模型實例
- **垃圾回收**: 及時清理大型中間結果

### 3. 並發處理優化

```python
import concurrent.futures

def parallel_search(queries: List[str]):
    with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
        futures = [executor.submit(search_single, q) for q in queries]
        results = [f.result() for f in futures]
    return results
```

### 4. 資料庫查詢優化

- **索引優化**: 合理設置 ChromaDB 索引參數
- **查詢批次化**: 減少資料庫連接次數
- **結果限制**: 避免檢索過多無用結果

## 🔧 擴展性設計

### 1. 插件式架構

```python
# 搜索引擎插件介面
class SearchEnginePlugin:
    def search(self, query: str, **kwargs) -> List[Dict]:
        raise NotImplementedError
    
    def get_name(self) -> str:
        raise NotImplementedError

# 重排序插件介面  
class RerankPlugin:
    def rerank(self, query: str, results: List[Dict]) -> List[Dict]:
        raise NotImplementedError
```

### 2. 多模型支持

```python
# 支持多種嵌入模型
SUPPORTED_EMBEDDING_MODELS = {
    "bge-m3": "BAAI/bge-m3",
    "bge-large": "BAAI/bge-large-zh-v1.5",
    "text2vec": "shibing624/text2vec-base-chinese"
}

# 支持多種重排序模型
SUPPORTED_RERANK_MODELS = {
    "bge-reranker": "BAAI/bge-reranker-base", 
    "bce-reranker": "maidalun1020/bce-reranker-base_v1"
}
```

### 3. API 介面預留

```python
# RESTful API 介面預留
class TongRAGAPI:
    def search(self, query: str, method: str = "semantic") -> Dict:
        # API搜索介面
        pass
    
    def rerank(self, query: str, results: List[Dict]) -> Dict:
        # API重排序介面
        pass
    
    def compare(self, query: str, methods: List[str]) -> Dict:
        # API對比介面  
        pass
```

### 4. 分散式部署支持

```python
# 分散式配置預留
DISTRIBUTED_CONFIG = {
    "enable_distributed": False,
    "coordinator_host": "localhost:8000",
    "worker_nodes": ["node1:8001", "node2:8002"],
    "load_balancer": "round_robin"
}
```

## 📈 監控與日誌

### 日誌系統

```python
# 分級日誌記錄
logger = logging.getLogger('tongrag3')

# 性能監控
@performance_monitor
def rerank_results(self, query: str, results: List[Dict]):
    start_time = time.time()
    # 重排序邏輯
    processing_time = time.time() - start_time
    self.logger.info(f"重排序處理時間: {processing_time:.3f}秒")
```

### 指標收集

```python
# 系統指標
METRICS = {
    "search_count": 0,
    "rerank_count": 0, 
    "average_response_time": 0.0,
    "success_rate": 0.95,
    "cache_hit_rate": 0.80
}
```

## 🔮 未來發展方向

### 短期目標 (v0.3.0)
- Web界面開發
- RESTful API實現  
- 搜索歷史記錄
- 多模型配置支持

### 中期目標 (v0.4.0 - v0.5.0)
- 分散式搜索架構
- 雲端同步功能
- 高級分析面板
- 自動化測試套件

### 長期願景 (v1.0.0+)
- 多語言界面支持
- 企業級部署方案
- AI增強搜索算法
- 開放插件生態系統

---

## 📞 技術支持

- **架構相關問題**: 請參考本文檔或提交 GitHub Issue
- **性能調優建議**: 查看性能優化章節
- **擴展開發指南**: 參考擴展性設計章節
- **最佳實踐**: 參考各功能模組的使用指南

**文檔版本**: v2.0  
**最後更新**: 2025-08-29  
**維護者**: TongRAG3 Architecture Team

---

*TongRAG3 - 讓智能檢索技術變得簡單易用*