測試程式新建立確認項目檢查表

|  |  |  |  |  |  |  |  |  |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Issue By | |  | Issue Date |  | | Tester : | |  | |
| 產品名稱 |  | | | 封裝格式 |  | | 外測廠 | |  |

是：該項確實有做到。

否：該項無法做到，但已經有跟直屬的分組主管討論過無法執行。

NA：不適用。

勾選不實者，該次新建立分數不予計分。

程式控管 :

|  |  |  |
| --- | --- | --- |
| 是 | 否 | 確認項目 |
|  |  | 提供給外測廠的量產程式是否已經程式原始檔移除。 |
| 部門政策，特殊緣故需臨時提供原始檔以做遠端連線 debug，事後須請外測廠移除。 | | |

初始良率掌控 :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 全測CP初始良率訂定 99%，三大批後再按實際良率狀況經主管確認後訂定之。  FT (抽測CP) 初始良率最低為 97%。 |
|  |  |  | o/s (含kelvin)比例FT : ≦1.0%(≧28pins); ≦0.5%(<28 pins). CP : ≦1.0%.  實際量產後仍應(再)以常態分布方式經主管確認後去增減. |

Only For CP :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | Multi Sites排線是否有防呆（ps.交換排線測試不會PASS或無法交換排線）。 |
| 交換排線測試不會PASS=>不同site的牛角,其腳位安排錯位或各site牛角給不同電阻值量值確認.  無法交換排線=>各site使用不同pin數的牛角. | | | |
|  |  |  | CP Trim電壓、電流、or其他特殊非準度用的fuse版本是否用讀取程式名稱執行 |
|  |  |  | CP Trim 不良、OTP/MTP Baking後掉code及(高低溫)、 CP FAIL且FT 無法宰出的不良 IC 是否將其打死，或其他可在FT辨識出來的方法。(純 CP 出貨不適用。) |
| 補充:每家FAB所提供的NVM IP，都各自有一套測試標準來保證MTP的品質,測試的Pattern會依照IP結構的不同而有很大的差異,但唯一的共通點就是都要CP1 -> Baking -> CP2 (for Data Retention);Baking Normally Condition : 250度 , 24hrs. | | | |
|  |  |  | Multi-side CP的max site是否在程式內部固定而非在 prg 檔變更及顯示site number。 |
|  |  |  | 測試資料是否顯示 X、Y 的座標位置。 |
| ![](data:image/png;base64...) | | | |
|  |  |  | 測試內有 trim (laser or current皆適用)者已經有建立correlation wafer，並在release form 內註明。 |
|  |  |  | 多站 CP 時, 如測試 map 有繼承使用時, 不同站的 SW.Bin (包含良品) 需分列不重複. 並於 release form 上註明前站不良Bin 不得在後站重測. |
| 例如cp2通常為測試cp1的pass die,則cp2不可去rt cp1的fail die,因測試項目不同的關係可能會將cp1的fail誤放. | | | |

Only for CP current Trim :

|  |  |  |  |
| --- | --- | --- | --- |
|  |  |  | Trim 前須 check fuse\_R，Trim後有check fuse的V(98% Vcc以上), I (5 M ohms以上)。 |
|  |  |  | LZB 使用前有無確認電壓>49.5V, (lzb\_16->set\_meas\_mode(LZB\_CAP\_VOLTAGE));及電流(外接電阻, Force程式使用最大電流/ Mesure >99%)才能使用，否則程式do\_dlog判定不良。 |
| ![](data:image/x-wmf;base64...)  lzb\_16->set\_voltage(0.0f);  lzb\_16->set\_current(0.01f);  wait.delay\_10\_us(50);  lzb\_16->close\_relay(LZB\_CONN\_CLAMP);  lzb\_16->close\_relay(LZB\_CONN\_OUT);  lzb\_16->close\_relay(LZB\_CONN\_GND);  lzb\_16->set\_meas\_mode(LZB\_CAP\_VOLTAGE);  wait.delay\_10\_us(150);  for(i=0 ; i<10 ; i++)sum=sum+lzb\_16->measure();  lzb\_cap\_v=sum/10;  if(lzb\_cap\_v < 49.5f)  {  lzb\_16->init();  goto end;  } | | | |

Only for CSP ( or 純CP 出貨) :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 出貨 wafer 不管廠內 or 委外測試都需執行 online EQC。(QC limt 不良皆為 SW. Bin31) |
|  |  |  | CSP wafer Bin-30 為 V/M(目檢) 不良勿使用。 |
|  |  |  | 已經有建立 correlation wafer，並在 release form 內註明。 |
|  |  |  | 多站測試時(包含 CP), 如測試 map 有繼承使用時, 不同站的 SW.Bin (包含良品) 需分列不重複. 並於 release form 上註明前站不良Bin 不得在後站重測. |
| 例如cp2通常為測試cp1的pass die,所以cp2不可去rt cp1的fail die,否則可能會因測試項目不同的關係將cp1的fail誤放. | | | |

Only for FT ( 含CSP、or 純CP 出貨) :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | Package FT 除超豐全系列及軌道測試系列外皆以 online EQC (100抽1)方式執行. (QC limt 不良皆為 SW. Bin31), 需在 release form 特別註明且備註單一H.W.Bin. |
|  |  |  | QFN （DFN）封裝如Thermal Pad為NC pin 須將其接到GND pin並檢查是否有接到 GND; 如需外測廠另加線路時需在文件註明並通知強制測試廠執行。 |
|  |  |  | 程式須加卡 O/S & Kelvin 都 PASS 才能續測。 |
|  |  |  | All pins O/S(須以 pin to pin short 方式測試) （含 NC，多NC 且無打線可以綁在一起測）及I/O Leakage必測。 |
|  |  |  | All pin測後加測 leakage & O/S。 |
|  |  |  | 所有的IQ、I\_SHDN、IQ\_SW、Leakage必須依常態分布來卡 SPEC。如正常品平均加減6倍標準差後不超過 +/- 0.3uA, 應以 +/- 0.3uA為上限值, 特例請備註. |
| 如下lxh leakage為例,average=-0.021uA,standard dev.為0.004uA,所以-0.021uA±6\*(0.004uA)=-0.045uA,  不超過±0.3uA, 應以±0.3uA為上下限值.  ![](data:image/png;base64...) | | | |
|  |  |  | All power pins的IQ& I\_SHDN全測，IQ\_SW（需check波形，特別是switching的第一根）。 |
|  |  |  | 所有power pin測後都需要加測IQ、I\_SHDN（BAT、同步的Boost converter的OUT當PVCC使用時者同），須加卡 I\_SHDN delta值。IQ\_SW則不加測，避免打傷無法check。 |
|  |  |  | 使用外部 relay時須檢查 relay 作動是否正常，並確定relay 作動異常時不會誤放。 |
|  |  |  | UVLO 規定測試手法為(Vin 須先在0V狀態) 先測 uvlo\_rise 再測 uvlo\_fall. |

Test Program :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | [**分Bin必須符合rule。**](#_SW_Bin_分Bin)(可點選超連結) |
|  |  |  | 無關items & 關sites防呆測試機的程式必須執行 Items check。（ps. Items被Disable在auto-run時會強制關閉並警告）。 |
|  |  |  | 測試 FBx（adj）input current須加測在operation mode（no switching）狀態下的值。 |
| ![](data:image/png;base64...)![](data:image/png;base64...)  Vout為FBx（adj）接分壓電阻而得,所以當FBx（adj）input current在operation mode過大會影響Vout準度. | | | |
|  |  |  | H/L/Floating active（In），active H/L/Off（Out）須測試相關Volt、Amp、Res；及Logic是否正確。以floating in 而言必須在開機前保持 floating 狀態再開機測試。 |
|  |  |  | Scan threshold 最小 scale 為 (max-min) / 20。如有遲滯規格需一併考量。 |
| 假如EN\_VTH L2H spec為1V~2V,則scan L2H 1step不得大於(2-1)V/20=50mV,如HYS spec為100~300mV,  則scan H2L 1step不得大於(300-100mV)/20=10mV. | | | |
|  |  |  | 量測電壓的 VI source 誤差 > 測試規範的 10%，須使用 HP meter 做 calibration。 |
| ![](data:image/png;base64...)  以G1625為例,Total output error的spec範圍為+-22mV即44mV,但Total output error的typical=18V,  所以檔位需用20V,以DVI300 measure精準度0.1% of range來看,worst case誤差為+-20mV>44mV\*10%,  因此需用HP meter對該assign的DVI300做calibration.做法請參考經驗分享的外掛HP meter.  ![](data:image/x-wmf;base64...) | | | |
|  |  |  | 使用 DVI-2K 量測Current Limit 項目，會造成 Current Clamp 者確認波形是否正常。 |

IQ、IQ\_SHDN、Leakage :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 加測Supply Voltage Range上限的IQ，spec 可以放寬一點。 |
| ![](data:image/png;base64...)  此 Case 的緣由是G9212V 發生的客訴，datasheet 已找不到，以G9109A替代。  客訴的現象是當 Vin = 12V時因耐壓不足會有數十mA的漏電  一般在測 IQ 的時候 Vin 會參照文件上的設定10V沒問題，在測 Line-reg時Vin的電壓雖設定 12V但是電流檔位一般會設滿檔300mA此時Vout也沒問題。  所以測IQ時除了datasheet上的Vin=10V再加測Vin=12V。  衍生的是高壓的產品 (超過5V製程的)，不管產品類型須要加測datasheet上所保證的操作電壓上限的IQ。下表為G5764的基本電性，參照此 rule 除了Vin=12V時的IQ外，Vin=28V的IQ也須加測。  ![](data:image/png;base64...) | | | |
|  |  |  | 正測Iq\_shdn 及 leakage 測試電壓皆以 operation voltage range 上限值測試. |
|  |  |  | Operation voltage 如低於 max. rating \* 80%, 已跟 RD confirm 以不低於此值測試漏電. |
| 以G5439為例, PS1\_IN absolute maximum ratings=20v,但PS1\_IN Input Voltage Range上限為13.2V,所以此PIN要測20v\*80%=16V的Leakage.  ![](data:image/png;base64...) ![](data:image/png;base64...) | | | |
|  |  |  | 測後加測的IQ及Leakage電壓以前次測項電壓之 90% 測試. |
|  |  |  | 當測試Output leakage在0V時(如buck的lx)，前端輸入該pin的電性規格保證電壓。 |
| ![](data:image/png;base64...)  ![](data:image/png;base64...)  上圖為 Boostrap Buck Converter的block diagram，當測試LX=0V(EN=0V)時的漏電除了datasheet上的Vin=12外，須加測 Vin=28V的 LX 漏電。  IQ\_SHDN 也是等同在測 Vin 的Leakage，除了標準的 Vin=12V外需加上Vin=28V時的 IQ\_SHDN。此時 LX 須設定0V & 拉載。  EN = 0V，Vin=28V、LX=0V測IQ\_SHDN跟Vin=28V、LX=0V測LX leak 的差異IQ\_SHDN > Ilk\_LX雖然不良恆不良，但可從數據中判讀是內部電路漏電亦或是大MOS漏電。  ![](data:image/png;base64...)  同理測試 BS 為高壓時應為High Side Turn ON狀態(如下圖)，BS 會從Vin 轉出5V的電壓，當 LX=0V時 BS=5V。而當上橋 turn on 時，LX=IN而BS=IN+5V，因此BS的漏電應當是在開機狀態下量測。但BS<=LX+5V的限制下，如high side 無法turn ON 100% duty 而會 switching 時會造成BS 對 LX 的跨壓過大而導致 IC damage。因此量測方式可能如下：   1. 在 shutdown 狀態下，保持 BS <= LX+5V循序先增加 LX 再增加BS電位至保證電壓後量測 BS 及 LX 的漏電流。 2. 在 Test Mode 下量測high side Ron 時可順便量測 BS 的漏電。   ![](data:image/png;base64...) | | | |
|  |  |  | 當輸出為 push-pull 架構且無法設定為 Hiz測試漏電時, 需在輸出各為High 及 Low 時測試該power pin的IQ. 多 pins 輸出可同時為High/Low時測試。 |

大電流 ( > 0.3A) :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 大電流須step上升(下降)，每一 step不超過 0.5A。 |
|  |  |  | Kelvin方式需卡contact R。  (PV3：Imax \* R\_force <14V。DVI\_2K：Imax \* (Rin\_Froce + Rout\_Force < 45V)。 |
| 如pv3需由vin對vout灌2A電流,則vin&vout Force的contact R需各<14V/2A=7ohm,因pv3的force sense間壓差不得大於14v,否則電流輸出會有問題.  如dvi\_2K需由vin對vout灌2A電流,因dvi\_2k電壓補償最多只到45v,假如vin=5v則vin+vout 的force contact R需<(45-5)v/2=20ohm. | | | |
|  |  |  | Rds\_on須PASS才能測試OC(SC)。 |
| 如mos沒開卻硬灌大電流,有damage ic風險. | | | |
|  |  |  | QC 程式是否將大電流測項(>1.0A)排除。 |

PWM 產品 (Buck、Boost、Buck-Boost Controller、Converter )：

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 同步Controller有無加測DH及DL切換的 dead time（non-overlap）。(2.0V to 2.0V) |
| ![](data:image/png;base64...)![](data:image/png;base64...)  因DH及DL如dead time過小或overlap會有效率不佳或shoot through的風險.  (controller的DH,DL同時high代表外推的mos同時turn on有shoot through的風險.)  ![](data:image/jpeg;base64...) | | | |
|  |  |  | 同步Converter有無加測 IQ\_SW，避免 shoot-thru。(LX需在近端用實體relay隔開) |
|  |  |  | 同步Converter 如有 true shut-down 或 pre-charge需加測. |
| 請參考G2206案例,pre-charge功能異常造成客退,最後加測pre-charge狀態.  ![](data:image/x-wmf;base64...) | | | |
|  |  |  | 如 switching 頻率是在 test mode 下保證時，需要加測 normal mode 能正常 switching，此時的 spec 可以放寬 2 倍。(ps. 原本卡 +/-10% -> 可卡 +/- 20%) |
|  |  |  | Buck全開全關的則加測Normal Mode 時的操作是否正常，FB電壓可 force spec的上下限，如因此不穩則可放寬到一半 ( ps. 原本卡 +/-2% -> +/-3%) |

Charger 產品 ( Linear or Switching Charger)：

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 有無加測Bat 到 Vin(Dcin, USBin, …whatever) 的漏電，Vin給0V抽載量Vin漏電。 |

Indicator ( Open Drain / Push Pull )：

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 所有 Indicator 都需在 EN為 H及L 時確認其狀態是否正常. |

Open Drain Output：

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 測試Leakage的電壓為Vcc\_max。此時Vcc則降為Vcc\_max-1.0V。  須再次確認 datasheet 上所規範的所有 open drain ，包含圖、文敘述。 |
| 此case為G765的客訴，用G67x 的電路圖來解釋。  一般 open drain 應該作成如下圖右，但RD不小心做了下圖左對天的 diode。  ![](data:image/png;base64...)  當客戶的應用電路如下圖左則無問題，但客戶應用電路是如下圖右，+5.0V的power 則會有漏電產生，因此產生客訴。  ![](data:image/png;base64...)  測試條件同下則無問題  ![](data:image/png;base64...)  測試條件如下則須修正為 Voc=5.5V，Vin=4.5V  ![](data:image/png;base64...) | | | |

XVI 使用 :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 使用前須確認溫度低於55C 才能續測。 |
| ![](data:image/png;base64...) | | | |
|  |  |  | 接在輸出 pin 時，電壓range 須大於輸出電壓，電流需大於range \* 1%，如該電流設定值會影響測試值則設定meter mode 或 open relay |
| 有實際案例在xvi floating時會吃電流,所以xvi在floating時需設為meter mode.  ![](data:image/x-wmf;base64...) | | | |

PE32H 使用 :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | ATPG 或 Pattern Compare 時需故意在最後的 pattern 比對反向 code, 使測試結果會 fail一個或以上的 pattern, 並比對該不良pattern的行數是否正確. |
| ![](data:image/x-wmf;base64...) | | | |
|  |  |  | 使用 PE32H 測試 NVM 產品的 data-retension 時, 除承上需比出1個或以上的不良外, 當比對結果為 0 fail 時需跳出視窗警告並將程式關閉. Ps因data-retension不良需打死. |
|  |  |  | Multi-sites 測試時如 compare pattern 會超過 8192行, 則需將 pattern 拆開並用 serial 方式測試. (非測試 sites 的 pattern 皆為 X (don’t care)). |

Communication protocol (ex : I2C) ACK確認 :

|  |  |  |  |
| --- | --- | --- | --- |
| 是 | 否 | NA | 確認項目 |
|  |  |  | 有 ATPG 僅測試一次 Read / Write 有發 ack 訊號 |
|  |  |  | 無 ATPG 需測試所有前門 code (register table) Read / Write的ack 訊號 |
| G751 有客訴無法讀取溫度TE測試PASS，經驗證後是 IC 沒有發 ACK 訊號。對系統來說，沒有ACK訊號則會hand 住無法繼續執行指令。 | | | |

以上符合大項的所有項目工程師需逐項核實確認, 如有勾選”否”者請於每大項最後新增一欄填寫原因並由課長級以上的主管確認後填寫檢查的主管姓名。

注意事項 :

1. 實際測試內容請參考測\\wss\TEST\TE\_工作記錄\TE測試相關文件及規定試必測項目及注意事項.doc.

以上如有疑義者, 可向主管 or 資深人員請教.

SW Bin 分Bin 規定

|  |  |  |  |
| --- | --- | --- | --- |
| SWBin | Describption | 站點 | Remark |
| 5 | Contact | All | First test item(exclude index time) |
| 6 | Kelvin & Contact R | All |  |
| 7 | Leakage | All |  |
| 8 | IQ | All |  |
| 17 | Chk\_Leakage | All |  |
| 18 | Chk\_IQ | All |  |
| 19 | Chk\_Contact | All | Last test item. |
| 21 | PAT | All |  |
| 25 | (Map combine) | CP/CSP | 僅有單片 wafer 需多型號(版本)需求保留 |
| 26 | (Map combine) | CP/CSP | 僅有單片 wafer 需多型號(版本)需求保留 |
| 27 | Data Retention | CP |  |
| 28 | Double IC Warning | FT S.W. | 偵測疊料用. |
| 29 | ATPG | All | 因MSEC CSP Bin30 為 VM fail, 2018/04/10 調整. |
| 30 | (MSEC CSP VM fail) | CSP | 僅MSEC CSP 產品測試保留Bin. |
| 31 | Online EQC fail | All |  |
| **32** | **Short** | **All** |  |
| 33 | (GTK CSP VM fail) | CSP | 僅GTK CSP 產品測試保留Bin. |
| 63 | GDBC | CP/CSP |  |

UF200 : CP or CSP僅能使用 Bin1 ~ 64, 超過 prober 上會顯示減去 64的值.

更多 S.W. Bin 請跟 CP 廠確認。