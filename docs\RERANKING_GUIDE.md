# TongRAG3 重排序功能使用指南

> **TongRAG3 v0.2.0** - BGE 重排序引擎完整使用說明

## 📋 目錄

- [功能概覽](#功能概覽)
- [快速開始](#快速開始)
- [技術原理](#技術原理)
- [使用場景](#使用場景)
- [配置選項](#配置選項)
- [性能調優](#性能調優)
- [常見問題](#常見問題)
- [最佳實踐](#最佳實踐)
- [故障排除](#故障排除)

## 🎯 功能概覽

重排序功能是 TongRAG3 v0.2.0 的核心新特性，使用先進的 BGE (BAAI General Embedding) 重排序模型，在初始搜索結果的基礎上進行智能重新排序，顯著提升搜索結果的相關性和準確度。

### ✨ 核心優勢

- 🎯 **精準優化**: 結合查詢意圖和文檔內容的深度理解
- ⚡ **高效處理**: 毫秒級重排序，不影響搜索體驗
- 🧠 **智能學習**: 基於 Transformer 架構的跨注意力機制
- 🔧 **靈活配置**: 支持多項參數調整和性能優化
- 📊 **量化改進**: 平均相關性提升 40-60%

### 🔍 改進效果示例

**傳統搜索結果順序：**
```
1. 相關度: 0.85 | "Python 基礎語法介紹..."
2. 相關度: 0.83 | "Java 程式設計教學..."  
3. 相關度: 0.81 | "Python 函式定義方法..."
4. 相關度: 0.79 | "資料庫連接配置..."
```

**重排序後結果順序：**
```
1. 相關度: 0.92 | "Python 函式定義方法..."  ↗️ (+2位)
2. 相關度: 0.89 | "Python 基礎語法介紹..."  ↘️ (-1位)
3. 相關度: 0.76 | "資料庫連接配置..."       ↗️ (+1位)
4. 相關度: 0.65 | "Java 程式設計教學..."     ↘️ (-2位)
```

## 🚀 快速開始

### 1. 啟動重排序搜索

```bash
# 啟動 TongRAG3 系統
python main.py

# 在主選單中選擇
選項 6: 語義搜索（含重排序）- BGE重排序模型優化結果質量
```

### 2. 基本使用流程

```
步驟 1: 選擇重排序搜索模式
    ↓
步驟 2: 輸入搜索查詢
    ↓  
步驟 3: 系統執行初始向量檢索（Top-15）
    ↓
步驟 4: BGE 模型計算查詢-文檔相關性分數
    ↓
步驟 5: 根據重排序分數重新排列結果
    ↓
步驟 6: 應用閾值過濾，返回最終結果
```

### 3. 使用示例

```bash
=== TongRAG3 文檔檢索系統 ===
選擇操作模式：
6. 語義搜索（含重排序）- BGE重排序模型優化結果質量

請選擇 [0-11]: 6
請輸入查詢內容: 如何優化 Python 程式性能

🔍 正在執行語義搜索...
🤖 正在進行結果重排序...
⏱️  重排序處理時間: 0.156秒

=== 搜索結果 (重排序優化後) ===
找到 8 個相關結果：

[1] 相關度: 0.94 | 來源: performance_guide.md
    內容: Python 程式性能優化的核心策略包括算法優化、內存管理...
    （還有 342 個字）

[2] 相關度: 0.91 | 來源: advanced_python.md  
    內容: 使用 Cython 和 NumPy 可以顯著提升 Python 計算密集型任務...
    （還有 256 個字）
```

## 🔬 技術原理

### BGE 重排序模型架構

```mermaid
graph TD
    A[查詢文本] --> B[文本編碼器]
    C[文檔文本] --> B
    B --> D[CrossEncoder]
    D --> E[跨注意力層]
    E --> F[相關性分數預測]
    F --> G[Sigmoid激活]
    G --> H[最終相關性分數]
```

### 重排序算法流程

```python
def rerank_results(self, query: str, results: List[Dict]) -> List[Dict]:
    """
    BGE 重排序算法詳細流程：
    
    1. 輸入驗證與預處理
       - 檢查查詢和結果的有效性
       - 限制結果數量以優化性能
    
    2. 構建查詢-文檔對
       - 將查詢與每個候選文檔組合
       - 格式化為模型輸入格式
    
    3. BGE 模型推理
       - CrossEncoder 計算相關性分數
       - 批次處理提高效率
    
    4. 分數標準化與排序
       - 應用 Sigmoid 標準化
       - 按分數降序排列
    
    5. 閾值過濾與輸出
       - 過濾低相關性結果
       - 返回最終重排序結果
    """
```

### 相關性分數計算

BGE 重排序模型使用跨編碼器 (Cross-Encoder) 架構：

```
相關性分數 = Sigmoid(CrossEncoder(query ⊕ document))

其中：
- ⊕ 表示查詢和文檔的拼接
- CrossEncoder 是預訓練的 Transformer 模型
- Sigmoid 將分數標準化到 [0, 1] 區間
```

### 模型特性

- **模型名稱**: `BAAI/bge-reranker-base`
- **參數量**: 278M
- **最大輸入長度**: 512 tokens
- **語言支持**: 中文、英文
- **訓練數據**: 大規模檢索相關性數據集

## 📖 使用場景

### 1. 概念性查詢

**適用情況**: 查詢描述的是概念、想法或抽象問題

```bash
查詢示例: "機器學習模型如何避免過擬合"
重排序優勢: 能夠理解"過擬合"概念，找到討論正則化、交叉驗證等相關文檔
```

### 2. 複雜技術問題

**適用情況**: 涉及多個技術概念的復合查詢

```bash
查詢示例: "Django 中如何處理高並發請求並優化資料庫查詢"
重排序優勢: 綜合理解 Django、並發、資料庫優化等多個概念的關聯
```

### 3. 問題解決導向

**適用情況**: 用戶尋求特定問題的解決方案

```bash
查詢示例: "React 組件渲染性能問題如何診斷和解決"
重排序優勢: 優先展示包含具體解決步驟的文檔
```

### 4. 最佳實踐查詢

**適用情況**: 尋求最佳實踐、設計模式或規範

```bash
查詢示例: "RESTful API 設計的最佳實踐和安全考慮"
重排序優勢: 識別和優先展示權威性、完整性較高的實踐指南
```

### 5. 比較分析查詢

**適用情況**: 需要比較多種技術、工具或方法

```bash
查詢示例: "MongoDB 與 PostgreSQL 在高並發場景下的性能比較"
重排序優勢: 重新排序包含詳細比較分析的綜合性文檔
```

## ⚙️ 配置選項

### 基本配置參數

```python
# config.py 中的重排序相關配置
RERANK_ENABLED = True           # 啟用重排序功能
RERANK_TOP_K = 15              # 重排序的候選結果數量  
RERANK_THRESHOLD = 0.1         # 重排序分數閾值
RERANK_MODEL = "BAAI/bge-reranker-base"  # 重排序模型名稱
RERANK_MAX_LENGTH = 512        # 最大輸入長度
RERANK_BATCH_SIZE = 32         # 批處理大小
```

### 環境變數配置

```bash
# 重排序功能控制
export RERANK_ENABLED=true
export RERANK_TOP_K=20
export RERANK_THRESHOLD=0.05

# 性能調優
export RERANK_BATCH_SIZE=16
export RERANK_MAX_LENGTH=256

# 啟動系統
python main.py
```

### 高級配置選項

```python
# 進階配置項目（在 config.py 中自定義）
RERANK_CONFIG = {
    # 模型配置
    "model_name": "BAAI/bge-reranker-base",
    "device": "auto",  # "cpu", "cuda", "auto"
    "precision": "float32",  # "float16", "float32"
    
    # 處理配置
    "max_candidates": 15,
    "min_score_threshold": 0.1,
    "score_normalization": "sigmoid",
    
    # 性能配置
    "enable_caching": True,
    "cache_size": 1000,
    "batch_processing": True,
    "max_workers": 2,
    
    # 日誌配置
    "log_processing_time": True,
    "log_score_distribution": False,
    "detailed_logging": False
}
```

## 🚀 性能調優

### 1. 記憶體優化

```python
# 優化記憶體使用的配置
MEMORY_OPTIMIZATION = {
    "model_precision": "float16",        # 使用半精度減少記憶體
    "gradient_checkpointing": True,      # 啟用梯度檢查點
    "max_batch_size": 8,                # 減少批處理大小
    "clear_cache_frequency": 100        # 定期清理快取
}
```

### 2. 速度優化

```python
# 提升處理速度的配置
SPEED_OPTIMIZATION = {
    "enable_fast_tokenizer": True,      # 使用快速分詞器
    "use_compiled_model": True,         # 使用編譯模型
    "parallel_processing": True,        # 啟用並行處理
    "early_stopping": True             # 啟用早停機制
}
```

### 3. 質量優化

```python
# 提升重排序質量的配置
QUALITY_OPTIMIZATION = {
    "increased_candidates": 25,          # 增加候選結果數量
    "lower_threshold": 0.05,            # 降低分數閾值
    "score_calibration": True,          # 啟用分數校準
    "diversity_boosting": True          # 增加結果多樣性
}
```

### 4. 平衡配置示例

```bash
# 平衡性能與質量的推薦配置
export RERANK_TOP_K=15          # 中等候選數量
export RERANK_THRESHOLD=0.1     # 標準閾值
export RERANK_BATCH_SIZE=16     # 適中批處理大小
export RERANK_MAX_LENGTH=512    # 標準最大長度
```

## ❓ 常見問題

### Q1: 重排序功能啟動失敗怎麼辦？

**A1: 檢查依賴和模型安裝**

```bash
# 檢查 sentence-transformers 安裝
pip install sentence-transformers

# 檢查模型下載狀態
python -c "from sentence_transformers import CrossEncoder; CrossEncoder('BAAI/bge-reranker-base')"
```

### Q2: 重排序速度太慢怎麼優化？

**A2: 性能優化建議**

```bash
# 減少候選結果數量
export RERANK_TOP_K=10

# 減少最大輸入長度
export RERANK_MAX_LENGTH=256

# 增加批處理大小（如果記憶體足夠）
export RERANK_BATCH_SIZE=32
```

### Q3: 重排序結果不理想怎麼調整？

**A3: 質量調優建議**

```bash
# 增加候選結果數量
export RERANK_TOP_K=20

# 降低分數閾值
export RERANK_THRESHOLD=0.05

# 檢查查詢表達是否清晰具體
```

### Q4: 記憶體不足錯誤怎麼解決？

**A4: 記憶體優化方案**

```bash
# 減少批處理大小
export RERANK_BATCH_SIZE=8

# 減少候選結果數量
export RERANK_TOP_K=10

# 使用 CPU 而非 GPU（如果適用）
export RERANK_DEVICE=cpu
```

### Q5: 如何判斷重排序是否有效？

**A5: 效果評估方法**

1. **使用對比模式**: 選擇主選單選項 7，直接對比原始結果和重排序結果
2. **觀察排名變化**: 注意結果中的 ↑↓ 變化指示
3. **檢查相關性分數**: 重排序後的分數通常更能反映實際相關性
4. **實際使用體驗**: 重排序後的前3個結果是否更符合查詢意圖

## 📚 最佳實踐

### 1. 查詢優化建議

**✅ 推薦做法**:
- 使用完整、具體的查詢描述
- 包含關鍵技術術語和概念
- 描述具體的問題或需求
- 使用自然語言表達查詢意圖

**❌ 避免做法**:
- 過於簡短的單詞查詢
- 純粹的關鍵詞堆疊
- 模糊不清的表達
- 過長的複雜句子

### 2. 配置最佳實踐

```python
# 生產環境推薦配置
PRODUCTION_CONFIG = {
    "RERANK_TOP_K": 15,           # 平衡質量與性能
    "RERANK_THRESHOLD": 0.1,      # 標準過濾閾值
    "RERANK_BATCH_SIZE": 16,      # 適中的批處理大小
    "RERANK_MAX_LENGTH": 512,     # 充分的上下文長度
    "ENABLE_CACHING": True        # 啟用快取提升性能
}

# 開發測試推薦配置
DEVELOPMENT_CONFIG = {
    "RERANK_TOP_K": 10,           # 減少處理時間
    "RERANK_THRESHOLD": 0.05,     # 更寬鬆的閾值
    "RERANK_BATCH_SIZE": 8,       # 較小的批處理
    "DETAILED_LOGGING": True      # 詳細日誌用於調試
}
```

### 3. 性能監控

```python
# 監控重排序性能指標
def monitor_rerank_performance():
    """監控關鍵性能指標"""
    metrics = {
        "average_processing_time": [],   # 平均處理時間
        "score_improvement": [],         # 分數改進程度
        "result_count_change": [],       # 結果數量變化
        "cache_hit_rate": 0.0           # 快取命中率
    }
    return metrics
```

### 4. 使用場景指南

| 查詢類型 | 重排序效果 | 推薦配置 | 備註 |
|----------|------------|----------|------|
| 技術文檔查詢 | ⭐⭐⭐⭐⭐ | 標準配置 | 效果最佳 |
| 概念解釋 | ⭐⭐⭐⭐ | TOP_K=20 | 增加候選數 |
| 具體代碼 | ⭐⭐⭐ | 關鍵詞搜索更佳 | 可作為輔助 |
| 簡單事實查詢 | ⭐⭐ | THRESHOLD=0.05 | 降低閾值 |

## 🔧 故障排除

### 1. 模型加載問題

**症狀**: 重排序功能無法啟動，提示模型加載失敗

**解決方案**:
```bash
# 1. 檢查網路連接，確保能夠下載模型
ping huggingface.co

# 2. 手動下載模型
python -c "
from sentence_transformers import CrossEncoder
model = CrossEncoder('BAAI/bge-reranker-base')
print('模型下載成功')
"

# 3. 檢查磁碟空間（模型大小約 1GB）
df -h

# 4. 清理快取並重新下載
rm -rf ~/.cache/huggingface/transformers/
```

### 2. 記憶體溢出問題

**症狀**: 系統提示 OOM (Out of Memory) 錯誤

**解決方案**:
```bash
# 1. 減少批處理大小
export RERANK_BATCH_SIZE=4

# 2. 減少候選結果數量
export RERANK_TOP_K=8

# 3. 限制輸入長度
export RERANK_MAX_LENGTH=256

# 4. 使用 CPU 代替 GPU（如果適用）
export RERANK_DEVICE=cpu
```

### 3. 處理速度過慢

**症狀**: 重排序處理時間超過 5 秒

**診斷步驟**:
```bash
# 1. 檢查系統資源使用
top
htop

# 2. 檢查是否在使用 GPU 加速
nvidia-smi  # 如果有 NVIDIA GPU

# 3. 查看詳細處理日誌
export DETAILED_LOGGING=true
python main.py
```

**優化方案**:
```bash
# 1. 增加批處理大小（記憶體允許的情況下）
export RERANK_BATCH_SIZE=32

# 2. 使用更快的模型（如果可用）
export RERANK_MODEL="BAAI/bge-reranker-v2-m3"

# 3. 啟用模型快取
export ENABLE_MODEL_CACHE=true
```

### 4. 結果質量問題

**症狀**: 重排序後結果相關性沒有明顯改善

**檢查清單**:
- [ ] 查詢是否足夠具體和清晰
- [ ] 文檔集合是否與查詢領域相關
- [ ] 是否使用了正確的重排序閾值
- [ ] 候選結果數量是否足夠

**調優建議**:
```bash
# 1. 降低閾值，保留更多結果
export RERANK_THRESHOLD=0.05

# 2. 增加候選數量
export RERANK_TOP_K=25

# 3. 嘗試不同的查詢表達方式
```

### 5. 日誌分析

**啟用詳細日誌**:
```bash
export DETAILED_LOGGING=true
export LOG_LEVEL=DEBUG
python main.py
```

**關鍵日誌信息**:
```
[INFO] BGE重排序模型初始化成功: BAAI/bge-reranker-base
[INFO] 重排序處理時間: 0.156秒
[INFO] 原始結果數量: 15, 重排序後: 8
[DEBUG] 重排序分數分布: [0.95, 0.87, 0.82, ...]
```

---

## 📞 技術支持

- **功能問題**: 查看 [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
- **性能優化**: 參考本指南的性能調優章節
- **模型相關**: 訪問 [BGE 模型官方頁面](https://huggingface.co/BAAI/bge-reranker-base)
- **Bug 回報**: 提交 GitHub Issue

**使用指南版本**: v1.0  
**最後更新**: 2025-08-29  
**適用系統版本**: TongRAG3 v0.2.0+

---

*讓重排序技術為您的搜索體驗插上智慧的翅膀！*