# Python虛擬環境
venv/
env/
ENV/
.venv/

# Python快取
__pycache__/
*.py[cod]
*$py.class
*.pyc
*.pyo
*.pyd
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# 文檔資料夾
doc/

# ChromaDB資料
chroma_data/

# 日誌檔案
logs/
*.log

# IDE設定
.vscode/
.idea/
*.swp
*.swo
.DS_Store
Thumbs.db

# 暫存檔案
*.tmp
*.temp
*.bak
*~
.cache/
.pytest_cache/

# 搜索結果導出
search_results_*.json
search_results_*.csv
search_results_*.txt

# Jupyter Notebook
.ipynb_checkpoints/

# 測試覆蓋率
.coverage
htmlcov/
.tox/
.nox/
coverage.xml
*.cover
.hypothesis/

# 環境變數
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 其他
*.db
*.sqlite3
.DS_Store
._.DS_Store
**/.DS_Store
**/._.DS_Store

# Windows
ehthumbs.db
Desktop.ini

# Linux
*~

# 系統檔案
.directory
.Trash-*

# MacOS
.AppleDouble
.LSOverride
Icon?

# 臨時檔案
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.coverage
.coverage.*
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json