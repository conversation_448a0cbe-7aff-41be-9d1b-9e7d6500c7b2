# coding=utf-8
# Copyright 2023 The HuggingFace Team. All rights reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# 2 GB
EXTERNAL_DATA_FORMAT_SIZE_LIMIT = 2 * 1024 * 1024 * 1024

ONNX_ENCODER_NAME = "encoder_model"
ONNX_DECODER_NAME = "decoder_model"
ONNX_DECODER_WITH_PAST_NAME = "decoder_with_past_model"
ONNX_DECODER_MERGED_NAME = "decoder_model_merged"

UNPICKABLE_ARCHS = [
    "encodec",
    "hubert",
    "sew",
    "sew-d",
    "speecht5",
    "unispeech",
    "unispeech-sat",
    "wav2vec2",
    "wav2vec2-conformer",
    "wavlm",
]

SDPA_ARCHS_ONNX_EXPORT_NOT_SUPPORTED = [
    "bart",
    "musicgen",
    "whisper",
]
