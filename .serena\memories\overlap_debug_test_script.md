# 重疊檢測調試測試腳本

## 問題
用戶重新導入文檔後，"塊 2"仍然顯示"重疊百分比: 0.0%"，說明重疊計算邏輯可能有問題。

## 調試方案
創建一個簡單的測試腳本來檢查：
1. 分塊位置計算是否正確
2. 重疊檢測邏輯是否正確
3. 句子邊界調整是否影響重疊

## 測試腳本
```python
# debug_overlap.py
import sys
sys.path.append('src')

from document_loader import DocumentLoader
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO)

# 創建測試文本
test_text = "這是第一段內容，用於測試重疊機制。第二段將會與第一段有部分重疊。第三段也會有重疊。第四段是最後的內容。"

# 創建 DocumentLoader
loader = DocumentLoader(chunk_size=100, chunk_overlap=50)  # 小塊便於測試

# 分塊測試
chunks = loader.split_text(test_text, "test.txt")

print(f"總共分成 {len(chunks)} 塊:")
for chunk in chunks:
    print(f"\n塊 {chunk['chunk_id']}:")
    print(f"  位置: {chunk['start_char']}-{chunk['end_char']}")
    print(f"  有重疊: {chunk['has_overlap']}")
    print(f"  重疊大小: {chunk['overlap_size']}")
    print(f"  重疊百分比: {chunk['overlap_percentage']:.1f}%")
    print(f"  文本: '{chunk['text'][:50]}...'")
    if chunk['overlap_text']:
        print(f"  重疊文本: '{chunk['overlap_text']}'")
    if chunk['new_content']:
        print(f"  新內容: '{chunk['new_content'][:50]}...'")
```

## 執行方式
```bash
cd d:\project\python\tongrag3
source venv/Scripts/activate
python debug_overlap.py
```

這將幫助我們確定重疊計算是否正確工作。