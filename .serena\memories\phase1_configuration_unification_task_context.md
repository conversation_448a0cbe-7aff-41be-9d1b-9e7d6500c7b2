# Phase 1 配置統一任務上下文

## 任務描述
根據用戶要求，實施配置清理改善計劃的第一階段：配置統一，消除重複的 get_config() 函式。

## 當前狀況分析
1. **重複配置函式確認**：
   - `src/config.py:37` - 原始的 get_config() 函式（50行簡單版本）
   - `src/config/__init__.py:103` - 新的結構化 get_config() 函式（420行，包含向後兼容）

2. **使用情況**：
   - 大部分模組已從 `src.config` 導入配置（新結構）
   - 新配置系統提供完整的向後兼容性
   - 現有系統功能正常

## 修正後的實施計劃

### Task 1.1: 分析和驗證 ✅
- 確認所有使用 config 的地方
- 驗證新配置系統已包含所有舊配置項
- 測試現有功能是否正常運作

### Task 1.2: 簡化配置結構
**修正重點**：不是刪除函式，而是：
1. 保留 `src/config.py` 作為簡單的重定向
2. 移除重複的 `get_config()` 函式定義
3. 改為從 `src.config` 導入所有必要項目
4. 添加 deprecation 註釋說明

### Task 1.3: 驗證和測試
- 運行所有 8 個搜索模式
- 確認配置值一致性
- 測試 UI 界面功能
- 特別測試：`python3 main.py --use-onnx` 互動式功能

## 預期效果
- 消除函式重複定義
- 保持 100% 向後兼容
- 代碼更清晰，維護更容易
- 為後續階段優化打基礎

## 驗證標準
- 所有搜索模式功能正常
- ONNX 模式能正常運行
- 配置導入無錯誤
- 向後兼容性完整保持