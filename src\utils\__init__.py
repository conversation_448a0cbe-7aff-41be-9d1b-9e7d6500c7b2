"""
Utils package for TongRAG3

This package contains utility functions and classes for the TongRAG3 system.
"""

# Import from existing overlap_detector module
from .overlap_detector import OverlapDetector

# Import from new modular structure
from .validation import validate_query
from .text import (
    convert_english_to_uppercase,
    clean_text,
    truncate_text,
    truncate_text_with_count
)
from .export import export_results
from .search import (
    format_results,
    calculate_relevance_score,
    get_search_stats
)

__all__ = [
    'OverlapDetector',
    'convert_english_to_uppercase',
    'validate_query',
    'export_results',
    'clean_text',
    'truncate_text',
    'truncate_text_with_count',
    'calculate_relevance_score',
    'get_search_stats',
    'format_results'
]