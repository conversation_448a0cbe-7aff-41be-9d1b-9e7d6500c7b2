# 搜索結果重疊顯示修復 - 完整實施計劃

## 技術需求總結
1. **修正 document_loader.py**: 正確計算重疊邏輯，第一塊永遠無重疊，其他塊基於實際位置計算
2. **增強 utils/search.py**: 分離重疊區和新內容，計算重疊百分比
3. **更新 ui/ui_search/display.py**: 實現兩級顯示（列表簡潔+詳細完整）
4. **不做向後兼容**: 舊文檔顯示為無重疊（正常行為）
5. **互動式驗證**: 在 venv 環境中使用 `python3 main.py --use-onnx` 測試

## 預期顯示效果

### 搜索列表（簡潔）
- 有重疊塊: 🟨重疊部分(完整) + 🟩新內容(截斷+剩餘字數)
- 無重疊塊: 🟩獨有內容(截斷+剩餘字數)

### 詳細查看（輸入編號後）
- 有重疊塊: 📊重疊分析 + 🟨重疊部分 + 🟩新內容 + 📄完整內容
- 無重疊塊: 🟩獨有內容(完整顯示所有文字)

## 關鍵技術點
1. positions 數組重構: 先計算所有位置再遍歷
2. 實際重疊計算: start_pos < previous_end
3. 文本分離邏輯: overlap_text = text[:overlap_size], new_content = text[overlap_size:]
4. 截斷顯示: truncate_text_with_count() 函數

## Agent分配
- Backend Developer: 修改 document_loader.py 和 search.py 核心邏輯
- Frontend Developer: 更新 UI 顯示邏輯
- Python Environment Specialist: venv 環境測試協調