# debug_overlap.py 腳本內容

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重疊檢測調試腳本
用於檢查文檔分塊和重疊計算是否正確工作
"""

import sys
import os
sys.path.append('src')

from document_loader import DocumentLoader
import logging

# 設置日誌
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')

def test_overlap_detection():
    """測試重疊檢測功能"""
    
    # 創建測試文本
    test_text = "這是第一段內容，用於測試重疊機制。第二段將會與第一段有部分重疊。第三段也會有重疊。第四段是最後的內容。"
    
    print("="*60)
    print("重疊檢測調試測試")
    print("="*60)
    
    # 創建 DocumentLoader
    loader = DocumentLoader(chunk_size=100, chunk_overlap=50)  # 小塊便於測試
    
    print(f"測試文本長度: {len(test_text)} 字符")
    print(f"配置: chunk_size={loader.chunk_size}, chunk_overlap={loader.chunk_overlap}")
    print(f"測試文本: '{test_text}'")
    print("\n" + "="*60)
    
    # 先測試位置計算
    positions = loader._calculate_chunk_positions(test_text)
    print(f"\n計算出的分塊位置:")
    for i, (start, end) in enumerate(positions):
        print(f"  塊 {i+1}: {start}-{end} (長度: {end-start})")
        if i > 0:
            prev_end = positions[i-1][1]
            overlap = prev_end - start if start < prev_end else 0
            print(f"    理論重疊: {overlap} 字符")
    
    print("\n" + "="*60)
    
    # 分塊測試
    chunks = loader.split_text(test_text, "test.txt")
    
    print(f"\n總共分成 {len(chunks)} 塊:")
    for chunk in chunks:
        print(f"\n塊 {chunk['chunk_id']}:")
        print(f"  位置: {chunk['start_char']}-{chunk['end_char']}")
        print(f"  有重疊: {chunk['has_overlap']}")
        print(f"  重疊大小: {chunk['overlap_size']}")
        print(f"  重疊百分比: {chunk['overlap_percentage']:.1f}%")
        print(f"  文本長度: {len(chunk['text'])} 字符")
        print(f"  文本: '{chunk['text']}'")
        if chunk['overlap_text']:
            print(f"  重疊文本: '{chunk['overlap_text']}'")
        if chunk['new_content'] and chunk['new_content'] != chunk['text']:
            print(f"  新內容: '{chunk['new_content']}'")
    
    print("\n" + "="*60)
    print("測試完成")

if __name__ == "__main__":
    test_overlap_detection()
```

## 使用方式
1. 將此腳本保存為 debug_overlap.py
2. 在項目根目錄執行：
   ```bash
   cd d:\project\python\tongrag3
   source venv/Scripts/activate
   python debug_overlap.py
   ```

這將幫助診斷重疊計算問題。