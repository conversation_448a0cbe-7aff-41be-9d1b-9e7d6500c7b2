"""
TongRAG3主介面模組

該模塊提供TongRAG3文檔檢索系統的主要入口點，
整合了所有UI子模組功能，提供完整的用戶交互體驗。
"""

import sys
from src.search_engine import ChromaSearchEngine
from src.ui import TongRAGInterface

# 為了向後相容，保留SearchInterface別名
SearchInterface = TongRAGInterface


def main():
    """主函數，程序入口點"""
    try:
        # 初始化搜索引擎
        search_engine = ChromaSearchEngine()
        
        # 啟用重排序功能
        if hasattr(search_engine, 'enable_reranker'):
            search_engine.enable_reranker()
            print("✅ 搜索引擎重排序功能已啟用")
        
        # 創建並運行界面
        interface = TongRAGInterface(search_engine)
        interface.run_interactive_session()
        
    except Exception as e:
        print(f"啟動失敗: {str(e)}")
        sys.exit(1)


# Main function available for import - use main.py for execution