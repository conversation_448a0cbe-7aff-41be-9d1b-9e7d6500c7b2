"""
Export utilities for TongRAG3

This module contains functions for exporting search results in various formats.
"""

import json
import csv
from datetime import datetime
from pathlib import Path
from typing import Dict, List


def export_results(results: List[Dict], format: str = "json", filename: str = None) -> str:
    """
    導出搜索結果到文件
    
    Args:
        results: 搜索結果列表
        format: 導出格式 (json, csv, txt)
        filename: 文件名（如果為None則自動生成）
        
    Returns:
        導出的文件路径
    """
    if not results:
        raise ValueError("沒有結果需要導出")
    
    # 生成文件名
    if filename is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"search_results_{timestamp}.{format}"
    
    # 確保文件擴展名正確
    if not filename.endswith(f'.{format}'):
        filename = f"{filename}.{format}"
    
    file_path = Path(filename).absolute()
    
    try:
        if format.lower() == "json":
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
        
        elif format.lower() == "csv":
            with open(file_path, 'w', encoding='utf-8', newline='') as f:
                if results:
                    writer = csv.DictWriter(f, fieldnames=results[0].keys())
                    writer.writeheader()
                    for result in results:
                        # 將複雜對象轉換為字符串
                        row = {}
                        for k, v in result.items():
                            if isinstance(v, (dict, list)):
                                row[k] = json.dumps(v, ensure_ascii=False)
                            else:
                                row[k] = str(v)
                        writer.writerow(row)
        
        elif format.lower() == "txt":
            with open(file_path, 'w', encoding='utf-8') as f:
                for i, result in enumerate(results, 1):
                    f.write(f"===== 結果 {i} =====\n")
                    f.write(f"ID: {result.get('id', 'N/A')}\n")
                    f.write(f"來源: {result.get('source', 'N/A')}\n")
                    f.write(f"相關度: {result.get('score_display', 'N/A')}\n")
                    f.write(f"內容:\n{result.get('text', 'N/A')}\n\n")
        
        else:
            raise ValueError(f"不支持的導出格式: {format}")
        
        return str(file_path)
        
    except Exception as e:
        raise IOError(f"導出文件失敗: {str(e)}")