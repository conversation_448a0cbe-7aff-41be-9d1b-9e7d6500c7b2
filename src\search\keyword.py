"""
Keyword Search Module

This module implements keyword-based search functionality including exact keyword
matching, regular expression pattern matching, and document filtering based on
text content patterns.
"""

import logging
import re
from typing import Dict, List, Optional, Any

from ..config import MAX_RESULTS
from ..utils import convert_english_to_uppercase, format_results
from .rerank_mixin import RerankerMixin

# 設置日志
logger = logging.getLogger(__name__)


class KeywordSearchMixin(RerankerMixin):
    """
    Mixin class providing keyword-based search functionality.
    
    This class implements various keyword search methods including exact matching,
    regular expressions, and content-based filtering using ChromaDB's text search
    capabilities.
    """
    
    def keyword_search(
        self, 
        keyword: str, 
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        關鍵詞搜索，在文檔內容中匹配關鍵詞。
        
        Args:
            keyword: 搜索關鍵詞
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            # Ensure connection exists
            if hasattr(self, '_ensure_connection'):
                self._ensure_connection()
            
            if not keyword.strip():
                return []
            
            # 將英文字母轉換為大寫
            processed_keyword = convert_english_to_uppercase(keyword)
            logger.debug(f"關鍵詞搜索 - 原關鍵詞: '{keyword}' -> 處理後: '{processed_keyword}'")
            
            n_results = min(n_results, MAX_RESULTS)
            
            # Check collection availability
            if not hasattr(self, 'collection') or self.collection is None:
                logger.error("Collection not available for keyword search")
                return []
            
            results = self.collection.query(
                query_texts=[processed_keyword],
                n_results=n_results,
                where_document={"$contains": processed_keyword}
            )
            
            # Format search results
            if hasattr(self, '_format_search_results'):
                formatted_results = self._format_search_results(results)
            else:
                formatted_results = self._default_format_results(results)
            
            # Apply reranking if requested and available
            if use_reranker and self.is_reranker_enabled():
                logger.debug("Applying reranking to keyword search results")
                formatted_results = self._apply_reranking(
                    query=processed_keyword,
                    search_results=formatted_results,
                    reranker_name=reranker_name,
                    top_k=n_results,
                    score_threshold=score_threshold
                )
            
            logger.debug(f"關鍵詞搜索完成: 返回 {len(formatted_results)} 個結果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"關鍵詞搜索失敗: {str(e)}")
            return []
    
    def regex_search(
        self, 
        pattern: str, 
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        正則表達式搜索，在文檔內容中使用正則匹配。
        
        Args:
            pattern: 正則表達式模式
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            # Ensure connection exists
            if hasattr(self, '_ensure_connection'):
                self._ensure_connection()
            
            if not pattern.strip():
                return []
            
            # 將英文字母轉換為大寫
            processed_pattern = convert_english_to_uppercase(pattern)
            logger.debug(f"正則搜索 - 原正則模式: '{pattern}' -> 處理後: '{processed_pattern}'")
            
            # 驗證正則表達式
            try:
                re.compile(processed_pattern)
            except re.error as e:
                logger.error(f"無效的正則表達式: {processed_pattern}, 錯誤: {str(e)}")
                return []
            
            n_results = min(n_results, MAX_RESULTS)
            
            # Check collection availability
            if not hasattr(self, 'collection') or self.collection is None:
                logger.error("Collection not available for regex search")
                return []
            
            # Check embedding dimension availability
            embedding_dim = getattr(self, 'embedding_dim', 1024)  # Default to 1024 for BGE-M3
            
            # 使用占位符嵌入向量進行查詢
            placeholder_embedding = [0.0] * embedding_dim
            
            results = self.collection.query(
                query_embeddings=[placeholder_embedding],
                n_results=n_results,
                where_document={"$regex": processed_pattern}
            )
            
            # Format search results
            if hasattr(self, '_format_search_results'):
                formatted_results = self._format_search_results(results)
            else:
                formatted_results = self._default_format_results(results)
            
            # Apply reranking if requested and available
            if use_reranker and self.is_reranker_enabled():
                logger.debug("Applying reranking to regex search results")
                formatted_results = self._apply_reranking(
                    query=pattern,  # Use original pattern for reranking query
                    search_results=formatted_results,
                    reranker_name=reranker_name,
                    top_k=n_results,
                    score_threshold=score_threshold
                )
            
            logger.debug(f"正則搜索完成: 返回 {len(formatted_results)} 個結果")
            return formatted_results
            
        except Exception as e:
            logger.error(f"正則表達式搜索失敗: {str(e)}")
            return []
    
    def multi_keyword_search(
        self,
        keywords: List[str],
        operator: str = "OR",
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        多關鍵詞搜索，支援 AND/OR 邏輯操作。
        
        Args:
            keywords: 關鍵詞列表
            operator: 邏輯操作符 ("AND" 或 "OR")
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            if not keywords or not any(k.strip() for k in keywords):
                return []
            
            # Filter out empty keywords
            valid_keywords = [k.strip() for k in keywords if k.strip()]
            if not valid_keywords:
                return []
            
            logger.debug(f"多關鍵詞搜索: {valid_keywords} with {operator}")
            
            if operator.upper() == "AND":
                return self._multi_keyword_and_search(
                    valid_keywords, n_results, use_reranker, reranker_name, score_threshold
                )
            else:  # OR
                return self._multi_keyword_or_search(
                    valid_keywords, n_results, use_reranker, reranker_name, score_threshold
                )
                
        except Exception as e:
            logger.error(f"多關鍵詞搜索失敗: {str(e)}")
            return []
    
    def _multi_keyword_and_search(
        self,
        keywords: List[str],
        n_results: int,
        use_reranker: bool,
        reranker_name: Optional[str],
        score_threshold: float
    ) -> List[Dict[str, Any]]:
        """
        AND 邏輯的多關鍵詞搜索 - 文檔必須包含所有關鍵詞。
        """
        if not keywords:
            return []
        
        # Start with results from first keyword
        results = self.keyword_search(
            keywords[0], 
            n_results=MAX_RESULTS,  # Get more results for filtering
            use_reranker=False  # Apply reranking at the end
        )
        
        # Filter results that contain all other keywords
        for keyword in keywords[1:]:
            processed_keyword = convert_english_to_uppercase(keyword)
            filtered_results = []
            
            for result in results:
                text = result.get("text", "").upper()
                if processed_keyword in text:
                    filtered_results.append(result)
            
            results = filtered_results
            if not results:  # No documents contain all keywords
                break
        
        # Sort by distance and limit results
        results.sort(key=lambda x: x.get("distance", 0.0))
        results = results[:n_results]
        
        # Apply reranking if requested
        if use_reranker and self.is_reranker_enabled() and results:
            query = " AND ".join(keywords)  # Combine keywords for reranking
            results = self._apply_reranking(
                query=query,
                search_results=results,
                reranker_name=reranker_name,
                top_k=n_results,
                score_threshold=score_threshold
            )
        
        return results
    
    def _multi_keyword_or_search(
        self,
        keywords: List[str],
        n_results: int,
        use_reranker: bool,
        reranker_name: Optional[str],
        score_threshold: float
    ) -> List[Dict[str, Any]]:
        """
        OR 邏輯的多關鍵詞搜索 - 文檔包含任一關鍵詞即可。
        """
        all_results = {}  # Use dict to avoid duplicates
        
        # Collect results from each keyword
        for keyword in keywords:
            keyword_results = self.keyword_search(
                keyword, 
                n_results=n_results * 2,  # Get more results for merging
                use_reranker=False  # Apply reranking at the end
            )
            
            for result in keyword_results:
                doc_id = result["id"]
                if doc_id not in all_results:
                    all_results[doc_id] = result
                else:
                    # Keep the result with better (lower) distance
                    existing_distance = all_results[doc_id].get("distance", 1.0)
                    new_distance = result.get("distance", 1.0)
                    if new_distance < existing_distance:
                        all_results[doc_id] = result
        
        # Convert to list and sort by distance
        results = list(all_results.values())
        results.sort(key=lambda x: x.get("distance", 0.0))
        results = results[:n_results]
        
        # Apply reranking if requested
        if use_reranker and self.is_reranker_enabled() and results:
            query = " OR ".join(keywords)  # Combine keywords for reranking
            results = self._apply_reranking(
                query=query,
                search_results=results,
                reranker_name=reranker_name,
                top_k=n_results,
                score_threshold=score_threshold
            )
        
        return results
    
    def phrase_search(
        self,
        phrase: str,
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        短語搜索，精確匹配整個短語。
        
        Args:
            phrase: 要搜索的短語
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            if not phrase.strip():
                return []
            
            # Treat phrase as exact keyword match
            return self.keyword_search(
                keyword=phrase,
                n_results=n_results,
                use_reranker=use_reranker,
                reranker_name=reranker_name,
                score_threshold=score_threshold
            )
            
        except Exception as e:
            logger.error(f"短語搜索失敗: {str(e)}")
            return []
    
    def wildcard_search(
        self,
        pattern: str,
        n_results: int = 5,
        use_reranker: bool = False,
        reranker_name: Optional[str] = None,
        score_threshold: float = 0.0
    ) -> List[Dict[str, Any]]:
        """
        通配符搜索，支援 * 和 ? 通配符。
        
        Args:
            pattern: 包含通配符的搜索模式
            n_results: 返回結果數量
            use_reranker: 是否使用重排序改善結果質量
            reranker_name: 指定使用的重排序模型名稱
            score_threshold: 重排序時的分數閾值
            
        Returns:
            搜索結果列表
        """
        try:
            if not pattern.strip():
                return []
            
            # Convert wildcard pattern to regex
            # Escape special regex characters except * and ?
            escaped_pattern = re.escape(pattern)
            # Convert wildcards: * -> .*, ? -> .
            regex_pattern = escaped_pattern.replace(r'\*', '.*').replace(r'\?', '.')
            # Ensure pattern matches word boundaries for better matching
            regex_pattern = f"\\b{regex_pattern}\\b"
            
            logger.debug(f"通配符搜索: '{pattern}' -> 正則: '{regex_pattern}'")
            
            return self.regex_search(
                pattern=regex_pattern,
                n_results=n_results,
                use_reranker=use_reranker,
                reranker_name=reranker_name,
                score_threshold=score_threshold
            )
            
        except Exception as e:
            logger.error(f"通配符搜索失敗: {str(e)}")
            return []
    
    def _default_format_results(self, results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """
        Default formatting for search results when main class method is not available.
        
        Args:
            results: Chroma query results
            
        Returns:
            Formatted results list
        """
        # 轉換為標準格式
        formatted_list = []
        if results and results.get('ids'):
            ids = results['ids'][0] if results['ids'] else []
            documents = results['documents'][0] if results.get('documents') else []
            metadatas = results['metadatas'][0] if results.get('metadatas') else []
            distances = results['distances'][0] if results.get('distances') else []
            
            for i, doc_id in enumerate(ids):
                result = {
                    "id": doc_id,
                    "text": documents[i] if i < len(documents) else "",
                    "metadata": metadatas[i] if i < len(metadatas) else {},
                    "distance": distances[i] if i < len(distances) else 0.0,
                    "source": metadatas[i].get("source", "") if i < len(metadatas) and metadatas[i] else ""
                }
                formatted_list.append(result)
        
        return format_results(formatted_list)