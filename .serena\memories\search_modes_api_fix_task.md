# TongRAG3 搜索模式API修復任務

## 任務背景
用戶報告了2個搜索模式的API錯誤：
- 模式4：混合搜索 - metadata_filter參數錯誤
- 模式5：元數據篩選搜索 - 缺少方法實現

## 具體錯誤
1. `HybridSearchMixin.hybrid_search() got an unexpected keyword argument 'metadata_filter'`
2. `'ChromaSearchEngine' object has no attribute 'metadata_filtered_search'`

## 需要修復的文件
- `test_real_search_overlap.py:35` - 移除metadata_filter參數
- `test_real_search_overlap.py:38` - 修復或實現metadata_filtered_search方法
- `src/search_engine.py` - 可能需要添加缺失的方法

## 期望結果
所有7個搜索模式都能正常工作，包括：
1. 語義搜索
2. 關鍵詞搜索  
3. 正則表達式搜索
4. 混合搜索（修復後）
5. 元數據篩選搜索（修復後）
6. 語義搜索（含重排序）
7. 對比模式