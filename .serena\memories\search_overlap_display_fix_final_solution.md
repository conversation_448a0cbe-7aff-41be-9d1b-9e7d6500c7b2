# 搜索結果重疊顯示問題最終解決方案

## 問題確認
用戶提供了對比：

**文檔瀏覽器（正確）**：
- 🟨 重疊部分 (100 字符, 19.0%)
- 🟩 新內容 (426 字符)

**搜索結果（錯誤）**：
- 🟩 獨有內容（完整）
- 重疊百分比: 0.0%

## 根本原因
在 `src/utils/search.py` 第100行：
```python
new_content = metadata.get("new_content", clean_content)  # 錯誤！
```

當 `metadata` 中有 `overlap_text` 但 `new_content` 為空時，它會回退到完整文本，導致重疊檢測失效。

## 修復方案
修改 `src/utils/search.py` 的邏輯：

```python
# 修復前（錯誤）
overlap_text = metadata.get("overlap_text", "")
new_content = metadata.get("new_content", clean_content)  # 問題在這裡
overlap_percentage = metadata.get("overlap_percentage", 0.0)

# 修復後（正確）
overlap_text = metadata.get("overlap_text", "")
new_content = metadata.get("new_content", "")  # 不要回退到完整文本
overlap_percentage = metadata.get("overlap_percentage", 0.0)

# 然後在後續邏輯中正確處理空值情況
if has_overlap and overlap_text and not new_content:
    # 如果有重疊文本但沒有新內容，從完整文本中計算
    if overlap_size > 0 and len(clean_content) >= overlap_size:
        new_content = clean_content[overlap_size:]
    else:
        new_content = clean_content

if not has_overlap or not overlap_text:
    # 沒有重疊時，新內容就是完整內容
    new_content = clean_content
```

## 實施步驟
1. 修改 `src/utils/search.py` 第100行
2. 調整後續的邏輯處理
3. 測試搜索結果顯示

這個修復將確保搜索結果與文檔瀏覽器顯示一致。