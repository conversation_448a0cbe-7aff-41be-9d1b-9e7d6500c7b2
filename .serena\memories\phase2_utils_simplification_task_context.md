# Phase 2 Utils 簡化任務上下文

## 任務描述
實施配置清理改善計劃的第二階段：Utils 模組簡化，消除動態導入並改善性能。

## 當前狀況分析
1. **複雜的動態導入**：
   - `src/utils/__init__.py` 使用 importlib 動態載入 `src/utils.py`
   - 造成啟動延遲和 IDE 支援不佳

2. **架構混亂**：
   - `src/utils.py` - 主要工具函式（11個函式）
   - `src/utils/` 目錄 - 只有 `__init__.py` 和 `overlap_detector.py`

3. **死代碼發現**：
   - `_format_file_size` (第517行) - 未被使用，可刪除

## 函式使用分析
- `convert_english_to_uppercase` - 被 4 個模組使用
- `format_results` - 被 3 個模組使用（含內部調用多個函式）
- `calculate_relevance_score` - 被 format_results 調用
- `validate_query` - 被 2 個模組使用
- `export_results` - 被 ui_search/display.py 使用
- `clean_text` - 被 format_results 調用
- `truncate_text_with_count` - 被 format_results 調用
- `truncate_text` - 獨立函式
- `get_search_stats` - 被 ui_search/display.py 使用
- `_enhance_result_with_overlap_display` - 被 format_results 調用（內部）
- `_format_file_size` - **死代碼，無調用**

## 依賴關係映射
```
format_results 函式內部調用：
├── calculate_relevance_score (第77行)
├── clean_text (第81行)
├── truncate_text_with_count (第88行)
├── OverlapDetector (第114行 - 從 src.utils.overlap_detector 導入)
└── _enhance_result_with_overlap_display (第136行)
```

## 目標模組結構
```
src/utils/
├── __init__.py          # 統一導出接口
├── text.py              # 文本處理函式
│   ├── clean_text
│   ├── truncate_text
│   ├── truncate_text_with_count
│   └── convert_english_to_uppercase
├── search.py            # 搜索相關函式
│   ├── format_results
│   ├── calculate_relevance_score
│   ├── get_search_stats
│   └── _enhance_result_with_overlap_display (內部)
├── export.py            # 導出功能
│   └── export_results
├── validation.py        # 驗證功能
│   └── validate_query
└── overlap_detector.py  # 現有模組（保持不變）
```

## 實施步驟
1. **創建子模組**（按依賴順序）：
   - validation.py (無依賴)
   - text.py (無依賴)
   - export.py (無依賴)
   - search.py (依賴 text.py，需處理循環導入)

2. **更新 __init__.py**：
   - 移除動態導入邏輯
   - 使用直接相對導入
   - 保持 __all__ 列表向後兼容

3. **更新導入路徑**（8個文件）：
   - src/search/semantic.py
   - src/search/engine.py
   - src/search/keyword.py
   - src/search/hybrid.py
   - src/ui/ui_menu.py
   - src/ui/ui_base.py
   - src/ui/ui_search/display.py
   - src/reranker/*.py (檢查是否受影響)

4. **清理工作**：
   - 刪除 `_format_file_size` 死代碼
   - 將絕對導入改為相對導入
   - 考慮清理調試 print 語句

## 預期效果
- **性能提升**：導入速度提升 2-3 倍（保守估計）
- **啟動時間**：減少 100-200ms
- **IDE 支援**：從無法自動完成到完全支援
- **代碼質量**：更清晰的模組結構和依賴關係

## 風險管理
1. **循環導入**：使用延遲導入（函式內導入）解決
2. **向後兼容**：保持所有原有導出和導入路徑
3. **測試覆蓋**：需要測試所有 8 個搜索模式和 ONNX 功能

## 驗證標準
- 所有搜索模式功能正常
- ONNX 模式正常運行
- 導入無錯誤
- IDE 自動完成功能正常
- 性能確實有提升