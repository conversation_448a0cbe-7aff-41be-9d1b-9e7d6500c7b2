"""
Search-related utilities for TongRAG3

This module contains functions for processing and formatting search results.
"""

from typing import Dict, List


def calculate_relevance_score(distance: float) -> float:
    """
    將 Chroma 距離轉換為 0-100 的相關度分數
    
    Args:
        distance: Chroma 返回的距離值 (0-2之間)
        
    Returns:
        相關度分數 (0-100)
    """
    if distance < 0:
        distance = 0
    elif distance > 2:
        distance = 2
    
    # 將距離轉換為相似度分數 (距離越小，相關度越高)
    similarity = 1.0 - (distance / 2.0)
    return similarity * 100.0


def get_search_stats(results: List[Dict]) -> Dict:
    """
    計算搜索結果統計信息
    
    Args:
        results: 搜索結果列表
        
    Returns:
        統計信息字典
    """
    if not results:
        return {
            "total_results": 0,
            "average_score": 0.0,
            "min_score": 0.0,
            "max_score": 0.0,
            "sources": {},
            "source_count": 0
        }
    
    # 統計相關度分數
    scores = [r.get("relevance_score", 0.0) for r in results]
    avg_score = sum(scores) / len(scores) if scores else 0.0
    
    # 統計來源分布
    sources = {}
    for result in results:
        source = result.get("source", "未知來源")
        sources[source] = sources.get(source, 0) + 1
    
    return {
        "total_results": len(results),
        "average_score": avg_score,
        "min_score": min(scores) if scores else 0.0,
        "max_score": max(scores) if scores else 0.0,
        "sources": sources,
        "source_count": len(sources)
    }


def format_results(results: List[Dict]) -> List[Dict]:
    """
    格式化 Chroma 和 search_engine 返回的結果，直接使用文檔原始重疊信息
    
    Args:
        results: 原始搜索結果列表
        
    Returns:
        格式化後的結果列表，保留原始文檔重疊狀態
    """
    # 使用延遲導入避免循環依賴
    from .text import clean_text, truncate_text_with_count
    
    formatted_results = []
    
    for i, result in enumerate(results):
        # 計算相關度分數
        distance = result.get("distance", 0.0)
        relevance_score = calculate_relevance_score(distance)
        
        # 提取和清理文本
        text = result.get("text", "")
        clean_content = clean_text(text)
        
        # 獲取元數據
        metadata = result.get("metadata", {})
        chunk_id = metadata.get("chunk_id", "")
        has_overlap = metadata.get("has_overlap", False)
        
        # 生成預覽和剩餘字數
        preview_text, remaining_chars = truncate_text_with_count(clean_content, 150)
        
        # 格式化結果，直接使用文檔原始重疊信息
        formatted_result = {
            "rank": i + 1,
            "id": result.get("id", f"result_{i}"),
            "text": clean_content,
            "preview": preview_text,
            "remaining_chars": remaining_chars,
            "full_text": clean_content,
            "source": result.get("source") or metadata.get("file_name", "未知來源"),
            "file_name": metadata.get("file_name", "未知來源"),
            "metadata": metadata,
            "distance": distance,
            "relevance_score": relevance_score,
            "score_display": f"{relevance_score:.1f}%",
            "content_type": "document_chunk",
            "content_description": "文檔塊",
            "chunk_info": f"塊 {chunk_id}" if chunk_id else "完整內容",
            "chunk_id": chunk_id,
            # 直接使用文檔原始重疊信息
            "has_overlap": has_overlap,
            "overlap_percentage": 0.0  # 文檔層級不計算百分比
        }
        
        formatted_results.append(formatted_result)
    
    return formatted_results


