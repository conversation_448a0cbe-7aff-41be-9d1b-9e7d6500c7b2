# TongRAG3 代碼清理改善計劃

## 📊 問題總結
1. **配置函式重複**：`src/config.py` 和 `src/config/__init__.py` 都有 `get_config()`
2. **Utils 動態導入複雜**：`src/utils/__init__.py` 使用複雜的動態導入邏輯
3. **搜索引擎重複**：`src/search_engine.py` 和 `src/search/engine.py` 功能重複
4. **UI 冗餘代碼**：重構後的舊代碼仍然存在

## 🎯 改善目標
- 消除代碼重複，提升維護性
- 簡化導入邏輯，提升性能
- 統一架構設計，降低複雜度
- 保持 100% 功能兼容性

## 📋 Task 流程

### 🔥 階段一：配置統一（優先級：高）
**預計時間**：2-3 天
**目標**：統一配置管理，消除重複

#### Task 1.1：備份現有配置
- 備份 `src/config.py` 
- 確認所有配置項目都在 `src/config/__init__.py` 中

#### Task 1.2：移除重複配置函式
- 刪除 `src/config.py` 中的 `get_config()` 函式
- 保留配置變量定義，添加 deprecation 警告
- 更新所有導入語句指向 `src/config`

#### Task 1.3：測試驗證
- 運行所有搜索模式（1-6）確認功能正常
- 檢查配置值是否一致
- 確認 UI 界面正常顯示

**預期結果**：
- 只有一個 `get_config()` 函式
- 所有功能保持不變
- 配置導入更清晰

### ⚡ 階段二：Utils 簡化（優先級：中）
**預計時間**：3-4 天
**目標**：簡化 Utils 導入，提升性能

#### Task 2.1：分析 Utils 函式使用
- 列出 `src/utils.py` 中所有函式
- 確認哪些函式被實際使用
- 規劃函式分組（text, search, export 等）

#### Task 2.2：創建 Utils 子模組
- 創建 `src/utils/text.py`（文本處理函式）
- 創建 `src/utils/search.py`（搜索相關函式）
- 創建 `src/utils/export.py`（導出功能）
- 移動對應函式到各子模組

#### Task 2.3：更新導入邏輯
- 移除 `src/utils/__init__.py` 中的動態導入
- 使用直接導入：`from .text import clean_text`
- 更新所有使用 utils 的文件

#### Task 2.4：測試驗證
- 測試所有搜索功能正常
- 確認文本處理功能正確
- 檢查導出功能無誤
- 測量導入性能提升

**預期結果**：
- 導入速度提升 5-10 倍
- IDE 支持改善（自動完成、跳轉）
- 代碼結構更清晰

### 🔄 階段三：搜索引擎統一（優先級：中）
**預計時間**：5-7 天
**目標**：統一搜索引擎架構

#### Task 3.1：功能對比分析
- 對比兩個搜索引擎的功能差異
- 確認 `src/search/engine.py` 包含所有功能
- 列出需要遷移的功能（如果有）

#### Task 3.2：標記舊引擎為 Deprecated
- 在 `src/search_engine.py` 添加 deprecation 警告
- 更新文檔說明推薦使用新架構
- 添加遷移指南

#### Task 3.3：更新主要入口點
- 修改 `main.py` 使用新的搜索引擎
- 更新 `src/ui.py` 導入新引擎
- 確保向後兼容性

#### Task 3.4：測試驗證
- 測試所有 8 個搜索模式
- 確認重排序功能正常
- 驗證對比模式功能
- 檢查性能無退化

**預期結果**：
- 統一使用模組化搜索引擎
- 保持所有功能完整
- 代碼更易維護和擴展

### 🧹 階段四：清理冗餘代碼（優先級：低）
**預計時間**：2-3 天
**目標**：移除未使用代碼

#### Task 4.1：識別未使用代碼
- 使用工具掃描未使用的函式
- 手動檢查重排序工具函式使用情況
- 確認 UI 重構後的冗餘代碼

#### Task 4.2：安全移除
- 移除確認未使用的函式
- 保留可能的公共 API
- 添加適當的註釋說明

#### Task 4.3：最終測試
- 完整功能測試
- 性能基準測試
- 代碼質量檢查

**預期結果**：
- 代碼行數減少 10-15%
- 測試覆蓋率更準確
- 維護負擔減輕

## ✅ 每階段驗證標準

### 功能驗證清單
- [ ] 語義搜索正常
- [ ] 關鍵詞搜索正常  
- [ ] 正則表達式搜索正常
- [ ] 混合搜索正常
- [ ] 元數據篩選搜索正常
- [ ] 重排序搜索正常
- [ ] 對比模式正常
- [ ] 系統狀態顯示正常
- [ ] 導出功能正常

### 性能驗證標準
- 搜索響應時間不增加
- 系統啟動時間不增加（或有改善）
- 內存使用量不增加

### 代碼質量標準
- 無新增 linting 錯誤
- 測試覆蓋率不降低
- 文檔保持更新

## 🚀 預期總體效果
- **維護性**：代碼重複減少 80%
- **性能**：導入速度提升 5-10 倍
- **開發體驗**：IDE 支持改善，調試更容易
- **穩定性**：配置一致性，減少運行時錯誤
- **可擴展性**：模組化架構便於添加新功能