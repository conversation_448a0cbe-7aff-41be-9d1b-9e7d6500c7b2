"""
Text processing utilities for TongRAG3

This module contains functions for text cleaning, formatting, and manipulation.
"""

import re
import string


def convert_english_to_uppercase(text: str) -> str:
    """
    將字符串中的英文字母轉換為大寫，保持中文和其他字符不變。
    
    Args:
        text: 輸入文字串
        
    Returns:
        轉換後的文字串（英文字母大寫，其他字符保持原樣）
        
    Examples:
        >>> convert_english_to_uppercase("Hello 世界 API test")
        "HELLO 世界 API TEST"
        >>> convert_english_to_uppercase("測試 cpu usage")
        "測試 CPU USAGE"
    """
    if not text:
        return text
    
    result = []
    for char in text:
        # 只對英文字母進行大寫轉換
        if char in string.ascii_letters:
            result.append(char.upper())
        else:
            result.append(char)
    
    return ''.join(result)


def clean_text(text: str) -> str:
    """
    清理和標準化文本
    
    Args:
        text: 原始文本
        
    Returns:
        清理後的文本
    """
    if not text:
        return ""
    
    # 統一換行符
    cleaned = text.replace('\r\n', '\n').replace('\r', '\n')
    
    # 移除行首行尾空白
    lines = [line.strip() for line in cleaned.split('\n')]
    
    # 過濾空行，但保留一些結構
    filtered_lines = []
    prev_empty = False
    
    for line in lines:
        if line:  # 非空行
            filtered_lines.append(line)
            prev_empty = False
        else:  # 空行
            if not prev_empty:  # 避免連續空行
                filtered_lines.append("")
            prev_empty = True
    
    # 重新組合並清理多餘空白
    cleaned = '\n'.join(filtered_lines)
    
    # 在每行內部清理多餘空白
    lines = cleaned.split('\n')
    cleaned_lines = []
    for line in lines:
        if line.strip():  # 非空行
            # 將多個空白字符替換為單個空格
            cleaned_line = re.sub(r'\s+', ' ', line.strip())
            cleaned_lines.append(cleaned_line)
        else:
            cleaned_lines.append("")  # 保留空行
    
    return '\n'.join(cleaned_lines).strip()


def truncate_text_with_count(text: str, max_length: int = 200) -> tuple:
    """
    截斷文本並返回剩餘字數
    
    Args:
        text: 要截斷的文本
        max_length: 最大長度
        
    Returns:
        (截斷後的文本, 剩餘字數)
    """
    if not text or len(text) <= max_length:
        return text, 0
    
    # 使用現有的截斷邏輯
    truncated = truncate_text(text, max_length)
    
    # 計算實際剩餘字數（排除"..."）
    truncated_clean = truncated.rstrip('...')
    remaining_chars = len(text) - len(truncated_clean)
    
    return truncated, max(0, remaining_chars)


def truncate_text(text: str, max_length: int = 200) -> str:
    """
    智能截斷文本，保持完整句子
    
    Args:
        text: 要截斷的文本
        max_length: 最大長度
        
    Returns:
        截斷後的文本
    """
    if not text or len(text) <= max_length:
        return text
    
    # 如果文本太短，直接截斷
    if max_length < 10:
        return text[:max_length] + "..."
    
    # 嘗試在句子邊界截斷
    sentence_endings = ['. ', '。', '！', '？', '!', '?', '\n']
    
    # 在最大長度附近查找句子結束符
    search_start = max(0, max_length - 50)
    search_end = min(len(text), max_length)
    
    best_cut = -1
    for i in range(search_end - 1, search_start, -1):
        for ending in sentence_endings:
            if text[i:i+len(ending)] == ending:
                best_cut = i + len(ending)
                break
        if best_cut != -1:
            break
    
    # 如果找到合適的截斷點
    if best_cut != -1 and best_cut < max_length:
        return text[:best_cut].strip()
    
    # 否則在單詞邊界截斷
    truncated = text[:max_length - 3]
    last_space = truncated.rfind(' ')
    if last_space > max_length * 0.8:  # 如果空格位置合理
        truncated = truncated[:last_space]
    
    return truncated.strip() + "..."