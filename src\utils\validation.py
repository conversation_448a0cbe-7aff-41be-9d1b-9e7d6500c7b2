"""
Validation utilities for TongRAG3

This module contains functions for validating user input and data.
"""

import re


def validate_query(query: str) -> bool:
    """
    驗證查詢字符串的有效性
    
    Args:
        query: 查詢字符串
        
    Returns:
        是否有效
    """
    if not query or not isinstance(query, str):
        return False
    
    # 清理查詢
    clean_query = query.strip()
    
    # 檢查長度
    if len(clean_query) == 0 or len(clean_query) > 1000:
        return False
    
    # 檢查是否只包含空白字符
    if not clean_query or clean_query.isspace():
        return False
    
    # 檢查惡意模式
    malicious_patterns = [
        r'<script.*?>',  # XSS
        r'javascript:',   # JavaScript injection
        r'eval\(',       # Code execution
        r'exec\(',       # Code execution
        r'__import__',   # Python import injection
    ]
    
    for pattern in malicious_patterns:
        if re.search(pattern, clean_query, re.IGNORECASE):
            return False
    
    return True