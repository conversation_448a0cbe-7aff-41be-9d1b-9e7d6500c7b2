"""
系統管理功能模組

處理系統狀態顯示、資料庫重建和所有系統相關的管理功能。
"""

import os
from typing import Dict, List
from datetime import datetime
from .ui_base import BaseUI


class SystemUI(BaseUI):
    """
    系統UI類別
    
    負責：
    - 系統狀態顯示和診斷
    - 向量資料庫重建
    - 文檔詳情查看和重疊分析
    - 系統管理相關功能
    """
    
    def show_system_status(self) -> None:
        """顯示系統狀態"""
        self.display_progress("獲取系統狀態")
        
        try:
            info = self.search_engine.get_collection_info()
            
            self.print_colored("=== 系統狀態 ===", "cyan")
            print(f"集合名稱: {info.get('name', 'N/A')}")
            print(f"文檔數量: {info.get('count', 0)}")
            print(f"連接狀態: {info.get('status', 'unknown')}")
            
            if info.get('status') == 'error':
                self.print_colored(f"錯誤信息: {info.get('error', '')}", "red")
            
            # 顯示文檔列表
            if info.get('count', 0) > 0:
                try:
                    print()
                    self.print_colored("=== 文檔列表 ===", "blue")
                    
                    # 獲取所有文檔
                    all_docs = self.search_engine.collection.get()
                    if all_docs and all_docs.get('metadatas'):
                        # 按文件名分組統計
                        file_stats = {}
                        for metadata in all_docs['metadatas']:
                            if metadata and 'file_name' in metadata:
                                file_name = metadata['file_name']
                                if file_name not in file_stats:
                                    file_stats[file_name] = {
                                        'chunks': 0,
                                        'path': metadata.get('file_path', ''),
                                        'size': metadata.get('file_size', 0)
                                    }
                                file_stats[file_name]['chunks'] += 1
                        
                        # 顯示文件統計
                        for i, (file_name, stats) in enumerate(file_stats.items(), 1):
                            print(f"{i}. {file_name}")
                            print(f"   📝 文檔塊: {stats['chunks']} 個")
                            if stats['size'] > 0:
                                # 格式化文件大小
                                size = stats['size']
                                if size < 1024:
                                    size_str = f"{size} B"
                                elif size < 1024 * 1024:
                                    size_str = f"{size / 1024:.1f} KB"
                                else:
                                    size_str = f"{size / (1024 * 1024):.1f} MB"
                                print(f"   📏 文件大小: {size_str}")
                            
                            # 顯示路徑（縮短顯示）
                            path = stats['path']
                            if len(path) > 60:
                                path = "..." + path[-57:]
                            print(f"   📁 路徑: {path}")
                            print()
                        
                        print(f"總計: {len(file_stats)} 個文件，{sum(s['chunks'] for s in file_stats.values())} 個文檔塊")
                        
                        # 詢問是否要查看文檔詳情
                        print()
                        view_details = input("要查看文檔詳細內容嗎？ [y/N]: ").strip().lower()
                        if view_details in ['y', 'yes', '是']:
                            self._show_document_details(file_stats, all_docs)
                        
                except Exception as e:
                    self.print_colored(f"無法獲取文檔列表: {str(e)}", "yellow")
            
            # 顯示查詢歷史統計
            print()
            if self.query_history:
                print(f"查詢歷史: {len(self.query_history)} 條記錄")
                if len(self.query_history) > 0:
                    print(f"最近查詢: {self.query_history[-1][:50]}...")
            else:
                print("查詢歷史: 暫無記錄")
            
            print(f"最後更新時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            self.print_colored(f"無法獲取系統狀態: {str(e)}", "red")
        
        input("\n按 Enter 繼續...")
    
    def _show_document_details(self, file_stats: dict, all_docs: dict) -> None:
        """顯示文檔詳細內容，包括重疊部分"""
        try:
            # 讓用戶選擇要查看的文件
            file_list = list(file_stats.keys())
            
            while True:
                self.print_colored("\n=== 選擇要查看的文件 ===", "cyan")
                for i, file_name in enumerate(file_list, 1):
                    print(f"{i}. {file_name} ({file_stats[file_name]['chunks']} 個文檔塊)")
                
                print("0. 返回")
                
                try:
                    choice = input("\n請選擇文件編號: ").strip()
                    if choice == '0':
                        break
                    
                    file_idx = int(choice) - 1
                    if 0 <= file_idx < len(file_list):
                        selected_file = file_list[file_idx]
                        self._show_file_chunks(selected_file, all_docs)
                    else:
                        self.print_colored("無效的選擇，請重新選擇", "yellow")
                        
                except ValueError:
                    self.print_colored("請輸入有效的數字", "yellow")
                except KeyboardInterrupt:
                    break
                    
        except Exception as e:
            self.print_colored(f"顯示文檔詳情時發生錯誤: {str(e)}", "red")
    
    def _show_file_chunks(self, file_name: str, all_docs: dict) -> None:
        """顯示特定文件的所有文檔塊，標示重疊部分"""
        try:
            # 過濾出該文件的所有chunks
            file_chunks = []
            texts = all_docs.get('documents', [])
            metadatas = all_docs.get('metadatas', [])
            ids = all_docs.get('ids', [])
            
            for i, metadata in enumerate(metadatas):
                if metadata and metadata.get('file_name') == file_name:
                    chunk_info = {
                        'id': ids[i] if i < len(ids) else f"chunk_{i}",
                        'text': texts[i] if i < len(texts) else "",
                        'metadata': metadata,
                        'chunk_id': metadata.get('chunk_id', 0),
                        'has_overlap': metadata.get('has_overlap', False),
                        'start_char': metadata.get('start_char', 0),
                        'end_char': metadata.get('end_char', 0)
                    }
                    file_chunks.append(chunk_info)
            
            # 按chunk_id排序
            file_chunks.sort(key=lambda x: x['chunk_id'])
            
            while True:
                self.clear_screen()
                self.print_colored(f"=== {file_name} 文檔塊詳情 ===", "cyan")
                print(f"總共 {len(file_chunks)} 個文檔塊")
                print()
                
                for i, chunk in enumerate(file_chunks, 1):
                    # 使用不同圖標區分重疊和非重疊塊
                    if chunk['has_overlap']:
                        icon = "🔗"
                        self.print_colored(f"{i}. {icon} 文檔塊 #{chunk['chunk_id']} [有重疊]", "yellow")
                    else:
                        icon = "📝"
                        print(f"{i}. {icon} 文檔塊 #{chunk['chunk_id']}")
                    
                    print(f"   字符範圍: {chunk['start_char']}-{chunk['end_char']}")
                    
                    # 計算並顯示重疊詳情
                    if chunk['has_overlap'] and i > 1:
                        prev_chunk = file_chunks[i-2]  # i-1 因為enumerate從1開始
                        overlap_chars = prev_chunk['end_char'] - chunk['start_char']
                        if overlap_chars > 0:
                            overlap_percent = (overlap_chars / (chunk['end_char'] - chunk['start_char'])) * 100
                            self.print_colored(f"   📎 重疊: {overlap_chars} 字符 ({overlap_percent:.1f}%)", "cyan")
                    
                    # 顯示文本預覽（前50字符）
                    preview = chunk['text'][:50].replace('\n', ' ')
                    if len(chunk['text']) > 50:
                        preview += "..."
                    print(f"   預覽: {preview}")
                    print()
                
                print("選項:")
                print("1-{}: 查看文檔塊詳細內容".format(len(file_chunks)))
                print("s: 顯示重疊分析")
                print("0: 返回文件選擇")
                
                try:
                    choice = input("\n請選擇: ").strip().lower()
                    if choice == '0':
                        break
                    elif choice == 's':
                        self._show_overlap_analysis(file_chunks)
                    else:
                        chunk_idx = int(choice) - 1
                        if 0 <= chunk_idx < len(file_chunks):
                            self._show_chunk_content(file_chunks[chunk_idx], file_chunks)
                        else:
                            self.print_colored("無效的選擇", "yellow")
                            input("按 Enter 繼續...")
                            
                except ValueError:
                    self.print_colored("請輸入有效的選項", "yellow")
                    input("按 Enter 繼續...")
                except KeyboardInterrupt:
                    break
                    
        except Exception as e:
            self.print_colored(f"顯示文件塊時發生錯誤: {str(e)}", "red")
            input("按 Enter 繼續...")
    
    def _show_chunk_content(self, chunk: dict, all_chunks: list) -> None:
        """顯示文檔塊的完整內容，標示重疊部分"""
        try:
            self.clear_screen()
            self.print_colored(f"=== 文檔塊 #{chunk['chunk_id']} 詳細內容 ===", "cyan")
            print(f"字符範圍: {chunk['start_char']}-{chunk['end_char']}")
            print(f"文檔塊ID: {chunk['id']}")
            print(f"是否有重疊: {'是' if chunk['has_overlap'] else '否'}")
            print()
            
            # 分析重疊內容
            if chunk['has_overlap'] and chunk['chunk_id'] > 1:
                # 找到前一個chunk
                prev_chunk = None
                for c in all_chunks:
                    if c['chunk_id'] == chunk['chunk_id'] - 1:
                        prev_chunk = c
                        break
                
                if prev_chunk:
                    self._highlight_overlap_content(chunk, prev_chunk)
            else:
                print("=" * 60)
                print(chunk['text'])
                print("=" * 60)
            
            print()
            input("按 Enter 返回...")
            
        except Exception as e:
            self.print_colored(f"顯示塊內容時發生錯誤: {str(e)}", "red")
            input("按 Enter 繼續...")
    
    def _highlight_overlap_content(self, current_chunk: dict, prev_chunk: dict) -> None:
        """高亮顯示重疊內容"""
        try:
            current_text = current_chunk['text']
            prev_text = prev_chunk['text']
            
            # 使用metadata中的位置信息計算實際重疊
            theoretical_overlap = prev_chunk['end_char'] - current_chunk['start_char']
            
            # 簡單的重疊檢測 - 找出相同的開始部分
            overlap_length = 0
            min_length = min(len(current_text), len(prev_text))
            
            # 從current_text的開頭和prev_text的結尾比較
            for i in range(min(theoretical_overlap + 50, min_length)):  # 基於理論重疊值搜索
                # 檢查current_text的前i個字符是否在prev_text的結尾部分
                current_start = current_text[:i]
                if prev_text.endswith(current_start) and len(current_start.strip()) > 10:
                    overlap_length = i
            
            print("=" * 80)
            self.print_colored(f"📊 重疊分析結果", "cyan")
            print(f"理論重疊: {theoretical_overlap} 字符")
            print(f"實際檢測: {overlap_length} 字符")
            print("=" * 80)
            
            if overlap_length > 0:
                overlap_text = current_text[:overlap_length]
                remaining_text = current_text[overlap_length:]
                overlap_percent = (overlap_length / len(current_text)) * 100
                
                self.print_colored(f"\n🟨 重疊部分 ({overlap_length} 字符, {overlap_percent:.1f}%):", "yellow")
                print("-" * 40)
                self.print_colored(overlap_text, "yellow")
                print("-" * 40)
                
                self.print_colored(f"\n🟩 新內容 ({len(remaining_text)} 字符):", "green") 
                print("-" * 40)
                print(remaining_text)
                print("-" * 40)
            else:
                print("⚠️ 未檢測到文本重疊（可能是句子邊界調整）")
                print("\n完整內容:")
                print("-" * 40)
                print(current_text)
                print("-" * 40)
            
            print("=" * 80)
            
        except Exception as e:
            self.print_colored(f"分析重疊時發生錯誤: {str(e)}", "red")
            print("=" * 60)
            print(current_chunk['text'])
            print("=" * 60)
    
    def _show_overlap_analysis(self, chunks: list) -> None:
        """顯示重疊分析概覽"""
        try:
            self.clear_screen()
            self.print_colored("=== 📊 重疊分析報告 ===", "cyan")
            
            overlap_chunks = [c for c in chunks if c['has_overlap']]
            non_overlap_chunks = [c for c in chunks if not c['has_overlap']]
            
            # 統計信息
            total_chars = chunks[-1]['end_char'] if chunks else 0
            total_overlap_chars = 0
            
            print(f"\n📈 基本統計:")
            print(f"  總文檔塊數: {len(chunks)}")
            self.print_colored(f"  🔗 有重疊的塊: {len(overlap_chunks)}", "yellow")
            print(f"  📝 無重疊的塊: {len(non_overlap_chunks)}")
            print(f"  總字符數: {total_chars}")
            print()
            
            # 視覺化圖表
            self.print_colored("📐 重疊結構圖:", "blue")
            print("-" * 70)
            
            for i, chunk in enumerate(chunks):
                chunk_id = chunk['chunk_id']
                start = chunk['start_char']
                end = chunk['end_char']
                length = end - start
                
                # 計算縮放比例（將字符映射到顯示寬度）
                scale = 50 / total_chars if total_chars > 0 else 1
                display_start = int(start * scale)
                display_length = max(1, int(length * scale))
                
                # 構建視覺化行
                line = " " * display_start
                
                if chunk['has_overlap'] and i > 0:
                    # 計算重疊部分
                    prev_chunk = chunks[i-1]
                    overlap_chars = prev_chunk['end_char'] - chunk['start_char']
                    overlap_display = max(1, int(overlap_chars * scale))
                    total_overlap_chars += overlap_chars
                    
                    # 顯示重疊部分和新內容
                    line += "🔗" * overlap_display + "█" * (display_length - overlap_display)
                    
                    self.print_colored(f"Chunk {chunk_id:2}: {line}", "yellow")
                    print(f"          [{start:4}-{end:4}] 重疊: {overlap_chars} 字符")
                else:
                    line += "█" * display_length
                    print(f"Chunk {chunk_id:2}: {line}")
                    print(f"          [{start:4}-{end:4}]")
            
            print("-" * 70)
            
            # 重疊統計詳情
            if total_overlap_chars > 0:
                overlap_percent = (total_overlap_chars / total_chars) * 100 if total_chars > 0 else 0
                print()
                self.print_colored("📊 重疊統計:", "green")
                print(f"  總重疊字符: {total_overlap_chars}")
                print(f"  重疊率: {overlap_percent:.1f}%")
                print(f"  平均每個重疊塊: {total_overlap_chars/len(overlap_chunks):.0f} 字符" if overlap_chunks else "")
            
            print()
            input("按 Enter 返回...")
            
        except Exception as e:
            self.print_colored(f"顯示重疊分析時發生錯誤: {str(e)}", "red")
            input("按 Enter 繼續...")
    
    def rebuild_vector_database(self) -> None:
        """重建向量資料庫"""
        try:
            self.clear_screen()
            self.print_colored("=== 重建向量資料庫 ===", "cyan")
            print()
            
            # 顯示當前狀態
            try:
                info = self.search_engine.get_collection_info()
                self.print_colored(f"📊 當前狀態: {info}", "blue")
                
                if info.get('count', 0) > 0:
                    print(f"⚠️  發現現有數據 ({info['count']} 個文檔塊)")
                    confirm = input("是否要清空現有資料庫並重新導入？ [y/N]: ").strip().lower()
                    if confirm not in ['y', 'yes', '是']:
                        self.print_colored("❌ 操作已取消", "yellow")
                        input("\n按 Enter 繼續...")
                        return
            except Exception as e:
                self.print_colored(f"⚠️  無法獲取當前狀態: {str(e)}", "yellow")
                
            # 獲取文檔目錄
            default_dir = "doc/"
            doc_dir = input(f"\n📁 請輸入文檔目錄路徑 (默認: {default_dir}): ").strip()
            if not doc_dir:
                doc_dir = default_dir
                
            # 檢查目錄存在
            if not os.path.exists(doc_dir):
                self.print_colored(f"❌ 目錄不存在: {doc_dir}", "red")
                input("\n按 Enter 繼續...")
                return
                
            # 開始重建過程
            self.print_colored(f"\n🔄 開始重建向量資料庫...", "yellow")
            
            # 清空現有集合
            if info.get('count', 0) > 0:
                print("🗑️  清空現有集合...")
                if self.search_engine.clear_collection():
                    self.print_colored("✅ 集合清空成功", "green")
                else:
                    self.print_colored("❌ 集合清空失敗", "red")
                    input("\n按 Enter 繼續...")
                    return
            
            # 導入文檔
            try:
                from src.document_loader import DocumentLoader
                
                print(f"📖 開始導入文檔: {doc_dir}")
                loader = DocumentLoader()
                raw_documents = loader.process_documents(doc_dir)
                
                if not raw_documents:
                    self.print_colored("❌ 未找到任何文檔", "red")
                    input("\n按 Enter 繼續...")
                    return
                
                print(f"📝 找到 {len(raw_documents)} 個文檔塊")
                
                # 轉換格式以符合搜索引擎期望
                documents = []
                for doc in raw_documents:
                    formatted_doc = {
                        "id": f"chunk_{doc['chunk_id']}",
                        "text": doc["text"],
                        "metadata": {
                            "chunk_id": doc["chunk_id"],
                            "file_name": doc["file_name"],
                            "file_path": doc["file_path"],
                            "start_char": doc["start_char"],
                            "end_char": doc["end_char"],
                            "has_overlap": doc["has_overlap"],
                            "created_time": doc["created_time"],
                            "file_size": doc["file_size"]
                        }
                    }
                    documents.append(formatted_doc)
                
                # 添加到資料庫
                print("💾 添加到向量資料庫...")
                if self.search_engine.add_documents(documents):
                    self.print_colored("✅ 文檔添加成功", "green")
                else:
                    self.print_colored("❌ 文檔添加失敗", "red")
                    input("\n按 Enter 繼續...")
                    return
                
                # 驗證結果
                final_info = self.search_engine.get_collection_info()
                self.print_colored(f"✅ 重建完成！集合狀態: {final_info}", "green")
                
                # 測試搜索
                print("\n🔍 測試搜索功能...")
                test_results = self.search_engine.semantic_search("測試", n_results=2)
                self.print_colored(f"搜索測試: 找到 {len(test_results)} 個結果", "blue")
                
                self.print_colored("\n🎉 向量資料庫重建成功！", "green")
                
            except Exception as e:
                self.print_colored(f"❌ 重建過程中發生錯誤: {str(e)}", "red")
                import traceback
                print(traceback.format_exc())
                
        except Exception as e:
            self.print_colored(f"❌ 重建失敗: {str(e)}", "red")
        
        input("\n按 Enter 繼續...")