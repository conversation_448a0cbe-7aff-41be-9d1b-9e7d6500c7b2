# Search Overlap Display Issue Inquiry

## Problem Description
User is experiencing inconsistency in search overlap display where:
1. Search results show "獨有區域（完整）" (Unique area - complete)
2. But document analysis shows actual overlap exists
3. When searching specific text, overlap sometimes appears correctly
4. There's confusion about whether "實際搜尋後的重疊區" (actual search overlap) and "文檔的重疊區" (document overlap) are different things

## Key Details from User's Output
- Document block #2 shows 19.0% overlap (100 characters out of 526)
- Overlap detection shows theoretical vs actual overlap both as 100 characters
- User sees "🟩 獨有區域（完整）" but document clearly has overlap section "🟨 重疊部分"
- This suggests a display logic inconsistency in the search results vs document analysis

## Investigation Needed
1. Check overlap detection logic in search results display
2. Verify consistency between search overlap detection and document overlap analysis
3. Examine why search results show "complete unique area" when overlap exists