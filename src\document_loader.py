"""Document Loading and Processing Module for TongRAG3."""

import re
import logging
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime

logger = logging.getLogger(__name__)

# Optional dependencies
try:
    import PyPDF2
    HAS_PDF = True
except ImportError:
    HAS_PDF = False
    
try:
    from docx import Document as DocxDocument
    HAS_DOCX = True
except ImportError:
    HAS_DOCX = False

try:
    from .config import CHUNK_SIZE, CHUNK_OVERLAP, SUPPORTED_EXTENSIONS
except ImportError:
    from config import CHUNK_SIZE, CHUNK_OVERLAP, SUPPORTED_EXTENSIONS


class DocumentLoader:
    """Document loader with intelligent text chunking capabilities."""
    
    def __init__(self, chunk_size: int = CHUNK_SIZE, chunk_overlap: int = CHUNK_OVERLAP):
        self.chunk_size = chunk_size
        self.chunk_overlap = chunk_overlap
        self.supported_extensions = SUPPORTED_EXTENSIONS
        self.sentence_endings = re.compile(r'[.!?。！？；]')
        self.paragraph_break = re.compile(r'\n\s*\n')
        
    def scan_directory(self, path: str) -> List[Path]:
        """Scan directory for supported document files."""
        dir_path = Path(path)
        if not dir_path.exists():
            raise FileNotFoundError(f"Directory not found: {path}")
        if not dir_path.is_dir():
            raise ValueError(f"Path is not a directory: {path}")
            
        files = []
        for ext in self.supported_extensions:
            files.extend(dir_path.rglob(f"*{ext}"))
        logger.info(f"Found {len(files)} supported files in {path}")
        return sorted(files)
    
    def load_document(self, file_path: Path) -> str:
        """Load document content based on file type."""
        if not file_path.exists():
            raise FileNotFoundError(f"File not found: {file_path}")
            
        suffix = file_path.suffix.lower()
        try:
            if suffix in ['.md', '.txt']:
                return self._load_text_file(file_path)
            elif suffix == '.pdf':
                return self._load_pdf_file(file_path)
            elif suffix == '.docx':
                return self._load_docx_file(file_path)
            else:
                raise ValueError(f"Unsupported file type: {suffix}")
        except Exception as e:
            logger.error(f"Error loading {file_path}: {str(e)}")
            raise
    
    def _load_text_file(self, file_path: Path) -> str:
        """Load text/markdown files with encoding detection."""
        for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()
            except UnicodeDecodeError:
                continue
        raise ValueError(f"Could not decode file {file_path}")
    
    def _load_pdf_file(self, file_path: Path) -> str:
        """Load PDF files."""
        if not HAS_PDF:
            raise ImportError("PyPDF2 not installed")
        with open(file_path, 'rb') as f:
            reader = PyPDF2.PdfReader(f)
            return "\n".join(page.extract_text() for page in reader.pages)
    
    def _load_docx_file(self, file_path: Path) -> str:
        """Load DOCX files."""
        if not HAS_DOCX:
            raise ImportError("python-docx not installed")
        doc = DocxDocument(file_path)
        return "\n".join(p.text for p in doc.paragraphs if p.text.strip())
    
    def split_text(self, text: str, file_path: str) -> List[Dict]:
        """Split text into chunks with intelligent sentence boundary detection."""
        if not text.strip():
            return []
            
        path_obj = Path(file_path)
        file_size = path_obj.stat().st_size if path_obj.exists() else 0
        created_time = datetime.now().isoformat()
        chunks = []
        
        # Calculate all positions first
        positions = self._calculate_chunk_positions(text)
        
        for i, (start_pos, end_pos) in enumerate(positions):
            chunk_text = text[start_pos:end_pos].strip()
            if chunk_text:
                # Check for actual overlap based on positions
                has_overlap = False
                overlap_size = 0
                overlap_text = ""
                new_content = chunk_text
                overlap_percentage = 0.0
                
                if i > 0:  # Not the first chunk
                    prev_end_pos = positions[i-1][1]
                    if start_pos < prev_end_pos:  # Actual overlap detected
                        has_overlap = True
                        overlap_size = prev_end_pos - start_pos
                        overlap_text = text[start_pos:prev_end_pos]
                        new_content = text[prev_end_pos:end_pos].strip()
                        overlap_percentage = (overlap_size / len(chunk_text)) * 100 if chunk_text else 0.0
                
                chunks.append({
                    "chunk_id": i + 1,
                    "text": chunk_text,
                    "file_name": path_obj.name,
                    "file_path": str(path_obj.absolute()),
                    "start_char": start_pos,
                    "end_char": end_pos,
                    "has_overlap": has_overlap,
                    "overlap_size": overlap_size,
                    "overlap_text": overlap_text,
                    "new_content": new_content,
                    "overlap_percentage": overlap_percentage,
                    "created_time": created_time,
                    "file_size": file_size
                })
                
        logger.info(f"Split text into {len(chunks)} chunks for {path_obj.name}")
        return chunks
    
    def _calculate_chunk_positions(self, text: str) -> List[Tuple[int, int]]:
        """Calculate optimal chunk positions with sentence boundaries."""
        positions = []
        text_len = len(text)
        current_pos = 0
        
        while current_pos < text_len:
            basic_end = min(current_pos + self.chunk_size, text_len)
            if basic_end >= text_len:
                positions.append((current_pos, text_len))
                break
                
            optimal_end = self._find_sentence_boundary(text, current_pos, basic_end)
            positions.append((current_pos, optimal_end))
            current_pos = max(current_pos + 1, optimal_end - self.chunk_overlap)
            
        return positions
    
    def _find_sentence_boundary(self, text: str, start: int, preferred_end: int) -> int:
        """Find optimal sentence boundary near preferred end position."""
        search_start = max(start + self.chunk_size // 2, preferred_end - 50)
        search_end = min(len(text), preferred_end + 50)
        search_text = text[search_start:search_end]
        
        # Try sentence endings first
        sentence_matches = list(self.sentence_endings.finditer(search_text))
        if sentence_matches:
            target_offset = preferred_end - search_start
            best_match = min(sentence_matches, key=lambda m: abs(m.end() - target_offset))
            return search_start + best_match.end()
            
        # Fallback to paragraph breaks
        paragraph_matches = list(self.paragraph_break.finditer(search_text))
        if paragraph_matches:
            target_offset = preferred_end - search_start
            best_match = min(paragraph_matches, key=lambda m: abs(m.start() - target_offset))
            return search_start + best_match.start()
            
        return preferred_end
    
    def process_documents(self, doc_dir: str) -> List[Dict]:
        """Process all documents in directory and return chunks."""
        files = self.scan_directory(doc_dir)
        all_chunks = []
        
        if not files:
            logger.warning(f"No supported files found in {doc_dir}")
            return all_chunks
            
        logger.info(f"Processing {len(files)} documents...")
        
        for i, file_path in enumerate(files, 1):
            try:
                content = self.load_document(file_path)
                chunks = self.split_text(content, str(file_path))
                all_chunks.extend(chunks)
                
                progress = (i / len(files)) * 100
                logger.info(f"Progress: {i}/{len(files)} ({progress:.1f}%) - "
                          f"Processed {file_path.name} -> {len(chunks)} chunks")
            except Exception as e:
                logger.error(f"Failed to process {file_path}: {str(e)}")
                continue
                
        logger.info(f"Total chunks created: {len(all_chunks)} from {len(files)} files")
        return all_chunks